<?php
/**
 * Monolith Design Co. - Configuration File
 * Contains all site-wide settings and database configuration
 * 
 * IMPORTANT: Update these settings for your production environment
 */

// Prevent direct access
if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Environment Detection
$is_production = (isset($_SERVER['HTTP_HOST']) && $_SERVER['HTTP_HOST'] !== 'localhost');

// Site Configuration
define('SITE_NAME', 'Monolith Design Co.');
define('SITE_TAGLINE', 'Engineering the Future of Structures');

// Auto-detect site URL or set manually for production
if (defined('SITE_URL_OVERRIDE')) {
    define('SITE_URL', SITE_URL_OVERRIDE);
} else {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $dir = dirname($_SERVER['SCRIPT_NAME']);
    $dir = ($dir === '/') ? '' : $dir;
    define('SITE_URL', $protocol . '://' . $host . $dir);
}

define('THEME_PATH', SITE_URL . '/assets');
define('ADMIN_PATH', SITE_URL . '/admin');

// Database Configuration
// PRODUCTION: Update these with your actual database credentials
define('DB_HOST', 'localhost');
define('DB_NAME', 'monolith_design');
define('DB_USER', 'root');
define('DB_PASS', 'root');
define('DB_CHARSET', 'utf8mb4');

// Design System Constants
define('PRIMARY_COLOR', '#1A1A1A');
define('SECONDARY_COLOR', '#F5F5F5');
define('ACCENT_COLOR', '#E67E22');
define('TEXT_COLOR', '#333333');

// Admin Configuration
// PRODUCTION: Change the admin email and password!
define('ADMIN_EMAIL', '<EMAIL>');
define('ADMIN_PASSWORD_HASH', password_hash('admin123', PASSWORD_DEFAULT));

// File Upload Settings
define('UPLOAD_PATH', __DIR__ . '/assets/images/uploads/');
define('UPLOAD_URL', SITE_URL . '/assets/images/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Error Reporting - Production Ready
if ($is_production) {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/error.log');
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Session Configuration - Production Security
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', $is_production ? 1 : 0); // Enable for HTTPS in production
ini_set('session.cookie_samesite', 'Strict');

// Timezone
date_default_timezone_set('UTC');

// Security Configuration
define('CSRF_TOKEN_NAME', 'monolith_csrf_token');
define('SESSION_LIFETIME', 7200); // 2 hours

// Security Headers (Production)
if ($is_production) {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
}

// Theme Options (defaults - these will be overridden by database values)
$theme_options = [
    'site_logo' => THEME_PATH . '/images/logo.svg',
    'site_logo_white' => THEME_PATH . '/images/logo-white.svg',
    'footer_logo' => THEME_PATH . '/images/logo-white.svg',
    'favicon' => THEME_PATH . '/images/favicon.ico',
    'accent_color' => ACCENT_COLOR,
    'phone_number' => '+****************',
    'email' => '<EMAIL>',
    'address' => '1247 Steel Avenue, Downtown District, Metropolis, CA 90210',
    'facebook_url' => 'https://facebook.com/monolithdesign',
    'twitter_url' => 'https://twitter.com/monolithdesign',
    'linkedin_url' => 'https://linkedin.com/company/monolithdesign',
    'instagram_url' => 'https://instagram.com/monolithdesign',
    'footer_tagline' => 'Crafting architectural excellence through innovative design and sustainable solutions.',
    'footer_show_company' => '1',
    'footer_show_services' => '1',
    'footer_show_projects' => '1',
    'footer_show_contact' => '1',
    'footer_show_newsletter' => '1',
    'newsletter_title' => 'Stay Updated',
    'newsletter_description' => 'Get the latest insights on architecture and design trends.',
    'footer_contact_title' => 'Get In Touch',
    'footer_contact_description' => 'Ready to start your next project? Get in touch with our team today.'
];

// Auto-create upload directory if it doesn't exist
if (!file_exists(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
}
?>
