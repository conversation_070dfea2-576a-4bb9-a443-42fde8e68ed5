<?php
/**
 * Project Details Page - Individual Project Showcase
 * Complete single-page template for project portfolio display
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get project slug from URL
$slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: ' . siteUrl('projects'));
    exit;
}

// Fetch project from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM projects WHERE slug = ? AND active = 1");
$stmt->execute([$slug]);
$project = $stmt->fetch();

if (!$project) {
    header('Location: ' . siteUrl('404'));
    exit;
}

// Get related projects
$stmt = $db->prepare("SELECT * FROM projects WHERE category = ? AND id != ? AND active = 1 ORDER BY created_at DESC LIMIT 3");
$stmt->execute([$project['category'], $project['id']]);
$related_projects = $stmt->fetchAll();

// Get recent projects if no related projects found
if (empty($related_projects)) {
    $stmt = $db->prepare("SELECT * FROM projects WHERE id != ? AND active = 1 ORDER BY created_at DESC LIMIT 3");
    $stmt->execute([$project['id']]);
    $related_projects = $stmt->fetchAll();
}

// Get next and previous projects for navigation
$stmt = $db->prepare("SELECT id, slug, title FROM projects WHERE id > ? AND active = 1 ORDER BY id ASC LIMIT 1");
$stmt->execute([$project['id']]);
$next_project = $stmt->fetch();

$stmt = $db->prepare("SELECT id, slug, title FROM projects WHERE id < ? AND active = 1 ORDER BY id DESC LIMIT 1");
$stmt->execute([$project['id']]);
$prev_project = $stmt->fetch();

// Parse project gallery images
$gallery_images = [];
if (!empty($project['gallery'])) {
    $gallery_images = json_decode($project['gallery'], true) ?: [];
}

$pageTitle = $project['title'] . ' - ' . SITE_NAME;
$pageDescription = $project['description'] ?: 'Explore our ' . $project['title'] . ' project showcase';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="<?php echo htmlspecialchars($project['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:image" content="<?php echo $project['featured_image'] ?: themeUrl('images/logo.svg'); ?>">
    <meta property="og:url" content="<?php echo siteUrl('project/' . $project['slug']); ?>">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($project['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="twitter:image" content="<?php echo $project['featured_image'] ?: themeUrl('images/logo.svg'); ?>">
    
    <!-- Project-specific styles -->
    <style>
        /* Breadcrumb Navigation */
        .breadcrumb-nav {
            background: #F8F9FA;
            padding: 1rem 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-nav .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6c757d;
        }
        
        .breadcrumb a {
            color: var(--accent-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .breadcrumb a:hover {
            color: #d66e1a;
            text-decoration: underline;
        }
        
        .breadcrumb-separator {
            margin: 0 0.5rem;
            color: #adb5bd;
        }
        
        /* Project Hero Section */
        .project-hero {
            height: 70vh;
            background-size: cover;
            background-position: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            min-height: 600px;
        }
        
        .project-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(rgba(26, 26, 26, 0.4), rgba(26, 26, 26, 0.7));
            z-index: 1;
        }
        
        .project-hero-content {
            position: relative;
            z-index: 2;
            max-width: 1000px;
            padding: 2rem;
        }
        
        .project-hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
            line-height: 1.2;
        }
        
        .project-category {
            display: inline-block;
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1.5rem;
        }
        
        .project-status {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 0.4rem 1rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-left: 1rem;
        }
        
        .project-status.in-progress {
            background: #ffc107;
            color: #212529;
        }
        
        .project-status.planning {
            background: #6f42c1;
        }
        
        /* Project Overview */
        .project-overview {
            padding: 4rem 0;
            background: white;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .project-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
        }
        
        .project-description h2 {
            color: var(--text-color);
            font-size: 2rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }
        
        .project-description h3 {
            color: var(--accent-color);
            font-size: 1.4rem;
            margin: 2rem 0 1rem;
            font-weight: 600;
        }
        
        .project-description p {
            margin-bottom: 1.5rem;
        }
        
        .project-description ul {
            margin: 1.5rem 0;
            padding-left: 2rem;
        }
        
        .project-description li {
            margin-bottom: 0.5rem;
        }
        
        /* Project Meta Information */
        .project-meta {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 2rem;
            height: fit-content;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .project-meta h3 {
            color: var(--text-color);
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
            text-align: center;
        }
        
        .meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #E9ECEF;
        }
        
        .meta-item:last-child {
            border-bottom: none;
        }
        
        .meta-label {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .meta-value {
            color: var(--text-color);
            font-weight: 500;
            text-align: right;
        }
        
        .meta-value.highlight {
            color: var(--accent-color);
            font-weight: 600;
        }
        
        /* Project Gallery */
        .project-gallery {
            padding: 4rem 0;
            background: #F8F9FA;
        }
        
        .gallery-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .section-title {
            text-align: center;
            color: var(--text-color);
            font-size: 2.5rem;
            margin-bottom: 3rem;
            font-weight: 600;
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .gallery-item {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }
        
        .gallery-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover img {
            transform: scale(1.05);
        }
        
        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            display: flex;
            align-items: flex-end;
            padding: 1.5rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }
        
        .gallery-caption {
            color: white;
            font-weight: 500;
        }
        
        /* Social Share Buttons */
        .share-section {
            background: white;
            padding: 3rem 0;
            text-align: center;
            border-top: 1px solid #E9ECEF;
        }
        
        .share-buttons {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .share-buttons h4 {
            color: var(--text-color);
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
        }
        
        .share-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .share-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .share-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .share-facebook { background: #1877f2; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        .share-email { background: #666; }
        
        /* Related Projects */
        .related-projects {
            padding: 4rem 0;
            background: #F8F9FA;
        }
        
        .related-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .related-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .related-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .related-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .related-card-content {
            padding: 1.5rem;
        }
        
        .related-card h3 {
            color: var(--text-color);
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }
        
        .related-card h3 a {
            color: inherit;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .related-card h3 a:hover {
            color: var(--accent-color);
        }
        
        .related-card .category {
            color: var(--accent-color);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }
        
        .related-card .status {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* Project Navigation */
        .project-navigation {
            padding: 3rem 0;
            background: white;
            border-top: 1px solid #E9ECEF;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 2rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: color 0.3s ease;
            max-width: 300px;
        }
        
        .nav-link:hover {
            color: var(--accent-color);
        }
        
        .nav-link.prev {
            text-align: left;
        }
        
        .nav-link.next {
            text-align: right;
            flex-direction: row-reverse;
        }
        
        .nav-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.3rem;
        }
        
        .nav-title {
            font-weight: 600;
            line-height: 1.3;
        }
        
        .view-all-projects {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--accent-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .view-all-projects:hover {
            background: #d66e1a;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
            color: white;
        }
        
        /* Lightbox for gallery */
        .lightbox {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
            padding: 2rem;
        }
        
        .lightbox.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .lightbox-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
        }
        
        .lightbox img {
            width: 100%;
            height: auto;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 8px;
        }
        
        .lightbox-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0.5rem;
        }
        
        .lightbox-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 1rem;
            cursor: pointer;
            font-size: 1.5rem;
            border-radius: 50%;
            transition: background 0.3s ease;
        }
        
        .lightbox-nav:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .lightbox-nav.prev {
            left: -60px;
        }
        
        .lightbox-nav.next {
            right: -60px;
        }
        
        /* Responsive Design */
        @media (max-width: 968px) {
            .overview-grid {
                grid-template-columns: 1fr;
                gap: 3rem;
            }
            
            .project-meta {
                order: -1;
            }
        }
        
        @media (max-width: 768px) {
            .project-hero h1 {
                font-size: 2.5rem;
            }
            
            .project-hero-content {
                padding: 1rem;
            }
            
            .share-links {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-container {
                flex-direction: column;
                text-align: center;
            }
            
            .related-grid {
                grid-template-columns: 1fr;
            }
            
            .gallery-grid {
                grid-template-columns: 1fr;
            }
            
            .lightbox-nav {
                display: none;
            }
        }
        
        @media (max-width: 480px) {
            .project-hero h1 {
                font-size: 2rem;
            }
            
            .gallery-container,
            .related-container {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Breadcrumb Navigation -->
    <section class="breadcrumb-nav">
        <div class="container">
            <nav class="breadcrumb">
                <a href="<?php echo siteUrl(); ?>">Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo siteUrl('projects'); ?>">Projects</a>
                <span class="breadcrumb-separator">›</span>
                <span><?php echo htmlspecialchars($project['title']); ?></span>
            </nav>
        </div>
    </section>

    <!-- Project Hero Section -->
    <section class="project-hero" style="background-image: url('<?php echo $project['featured_image'] ?: themeUrl('images/hero-bg-1.jpg'); ?>');">
        <div class="project-hero-content">
            <?php if ($project['category']): ?>
                <div class="project-category"><?php echo htmlspecialchars($project['category']); ?></div>
            <?php endif; ?>
            <h1><?php echo htmlspecialchars($project['title']); ?></h1>
            <?php if ($project['status']): ?>
                <div class="project-status <?php echo strtolower(str_replace(' ', '-', $project['status'])); ?>">
                    <?php echo htmlspecialchars($project['status']); ?>
                </div>
            <?php endif; ?>
            <?php if ($project['description']): ?>
                <p style="font-size: 1.2rem; margin-top: 1.5rem; opacity: 0.9; max-width: 600px; margin-left: auto; margin-right: auto;">
                    <?php echo htmlspecialchars($project['description']); ?>
                </p>
            <?php endif; ?>
        </div>
    </section>

    <!-- Project Overview -->
    <section class="project-overview">
        <div class="container">
            <div class="overview-grid">
                <div class="project-description">
                    <h2>Project Overview</h2>
                    <?php echo $project['content']; ?>
                </div>

                <div class="project-meta">
                    <h3>Project Details</h3>
                    
                    <?php if ($project['client']): ?>
                    <div class="meta-item">
                        <span class="meta-label">Client</span>
                        <span class="meta-value highlight"><?php echo htmlspecialchars($project['client']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($project['location']): ?>
                    <div class="meta-item">
                        <span class="meta-label">Location</span>
                        <span class="meta-value"><?php echo htmlspecialchars($project['location']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($project['start_date']): ?>
                    <div class="meta-item">
                        <span class="meta-label">Start Date</span>
                        <span class="meta-value"><?php echo date('F Y', strtotime($project['start_date'])); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($project['end_date']): ?>
                    <div class="meta-item">
                        <span class="meta-label">Completion</span>
                        <span class="meta-value"><?php echo date('F Y', strtotime($project['end_date'])); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($project['budget']): ?>
                    <div class="meta-item">
                        <span class="meta-label">Budget</span>
                        <span class="meta-value highlight"><?php echo htmlspecialchars($project['budget']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($project['area']): ?>
                    <div class="meta-item">
                        <span class="meta-label">Area</span>
                        <span class="meta-value"><?php echo htmlspecialchars($project['area']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($project['category']): ?>
                    <div class="meta-item">
                        <span class="meta-label">Category</span>
                        <span class="meta-value"><?php echo htmlspecialchars($project['category']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($project['status']): ?>
                    <div class="meta-item">
                        <span class="meta-label">Status</span>
                        <span class="meta-value highlight"><?php echo htmlspecialchars($project['status']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Gallery -->
    <?php if (!empty($gallery_images)): ?>
    <section class="project-gallery">
        <div class="container">
            <div class="gallery-container">
                <h2 class="section-title">Project Gallery</h2>
                <div class="gallery-grid">
                    <?php foreach ($gallery_images as $index => $image): ?>
                    <div class="gallery-item" onclick="openLightbox(<?php echo $index; ?>)">
                        <img src="<?php echo htmlspecialchars($image['url'] ?? $image); ?>" 
                             alt="<?php echo htmlspecialchars($image['caption'] ?? $project['title'] . ' - Image ' . ($index + 1)); ?>">
                        <?php if (!empty($image['caption'])): ?>
                        <div class="gallery-overlay">
                            <div class="gallery-caption"><?php echo htmlspecialchars($image['caption']); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Social Share Section -->
    <section class="share-section">
        <div class="container">
            <div class="share-buttons">
                <h4>Share This Project</h4>
                <div class="share-links">
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(siteUrl('project/' . $project['slug'])); ?>" 
                       target="_blank" class="share-link share-facebook">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        Facebook
                    </a>
                    <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(siteUrl('project/' . $project['slug'])); ?>&text=<?php echo urlencode('Check out this amazing project: ' . $project['title']); ?>" 
                       target="_blank" class="share-link share-twitter">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        Twitter
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(siteUrl('project/' . $project['slug'])); ?>" 
                       target="_blank" class="share-link share-linkedin">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn
                    </a>
                    <a href="mailto:?subject=<?php echo urlencode('Check out this project: ' . $project['title']); ?>&body=<?php echo urlencode('I thought you might be interested in this project: ' . siteUrl('project/' . $project['slug'])); ?>" 
                       class="share-link share-email">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 .02l-12 6.51v11.47h24v-11.47zm0 2.03l8.74 4.67h-17.48zm-10 6.47h20v9h-20z"/>
                        </svg>
                        Email
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Navigation -->
    <section class="project-navigation">
        <div class="container">
            <div class="nav-container">
                <?php if ($prev_project): ?>
                <a href="<?php echo siteUrl('project/' . $prev_project['slug']); ?>" class="nav-link prev">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                    </svg>
                    <div>
                        <div class="nav-label">Previous Project</div>
                        <div class="nav-title"><?php echo htmlspecialchars($prev_project['title']); ?></div>
                    </div>
                </a>
                <?php endif; ?>
                
                <a href="<?php echo siteUrl('projects'); ?>" class="view-all-projects">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                    </svg>
                    View All Projects
                </a>
                
                <?php if ($next_project): ?>
                <a href="<?php echo siteUrl('project/' . $next_project['slug']); ?>" class="nav-link next">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                    <div>
                        <div class="nav-label">Next Project</div>
                        <div class="nav-title"><?php echo htmlspecialchars($next_project['title']); ?></div>
                    </div>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Related Projects -->
    <?php if (!empty($related_projects)): ?>
    <section class="related-projects">
        <div class="container">
            <div class="related-container">
                <h2 class="section-title">Related Projects</h2>
                <div class="related-grid">
                    <?php foreach ($related_projects as $related): ?>
                    <div class="related-card">
                        <?php if ($related['featured_image']): ?>
                            <img src="<?php echo $related['featured_image']; ?>" alt="<?php echo htmlspecialchars($related['title']); ?>">
                        <?php else: ?>
                            <img src="<?php echo themeUrl('images/hero-bg-1.jpg'); ?>" alt="<?php echo htmlspecialchars($related['title']); ?>">
                        <?php endif; ?>
                        <div class="related-card-content">
                            <?php if ($related['category']): ?>
                                <div class="category"><?php echo htmlspecialchars($related['category']); ?></div>
                            <?php endif; ?>
                            <h3><a href="<?php echo siteUrl('project/' . $related['slug']); ?>"><?php echo htmlspecialchars($related['title']); ?></a></h3>
                            <?php if ($related['status']): ?>
                                <div class="status"><?php echo htmlspecialchars($related['status']); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Lightbox for Gallery -->
    <div class="lightbox" id="lightbox">
        <div class="lightbox-content">
            <button class="lightbox-close" onclick="closeLightbox()">&times;</button>
            <button class="lightbox-nav prev" onclick="prevImage()">‹</button>
            <img src="" alt="" id="lightbox-img">
            <button class="lightbox-nav next" onclick="nextImage()">›</button>
        </div>
    </div>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>

    <!-- JavaScript for Gallery and Interactions -->
    <script>
        let currentImageIndex = 0;
        const galleryImages = <?php echo json_encode($gallery_images); ?>;

        function openLightbox(index) {
            currentImageIndex = index;
            const lightbox = document.getElementById('lightbox');
            const img = document.getElementById('lightbox-img');
            
            const imageData = galleryImages[index];
            const imageSrc = typeof imageData === 'string' ? imageData : imageData.url;
            const imageAlt = typeof imageData === 'string' ? 'Gallery Image' : (imageData.caption || 'Gallery Image');
            
            img.src = imageSrc;
            img.alt = imageAlt;
            lightbox.classList.add('active');
            
            // Disable body scroll
            document.body.style.overflow = 'hidden';
        }

        function closeLightbox() {
            const lightbox = document.getElementById('lightbox');
            lightbox.classList.remove('active');
            
            // Re-enable body scroll
            document.body.style.overflow = '';
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
            const img = document.getElementById('lightbox-img');
            const imageData = galleryImages[currentImageIndex];
            const imageSrc = typeof imageData === 'string' ? imageData : imageData.url;
            const imageAlt = typeof imageData === 'string' ? 'Gallery Image' : (imageData.caption || 'Gallery Image');
            
            img.src = imageSrc;
            img.alt = imageAlt;
        }

        function prevImage() {
            currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
            const img = document.getElementById('lightbox-img');
            const imageData = galleryImages[currentImageIndex];
            const imageSrc = typeof imageData === 'string' ? imageData : imageData.url;
            const imageAlt = typeof imageData === 'string' ? 'Gallery Image' : (imageData.caption || 'Gallery Image');
            
            img.src = imageSrc;
            img.alt = imageAlt;
        }

        // Close lightbox with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLightbox();
            } else if (e.key === 'ArrowRight') {
                nextImage();
            } else if (e.key === 'ArrowLeft') {
                prevImage();
            }
        });

        // Close lightbox when clicking outside the image
        document.getElementById('lightbox').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLightbox();
            }
        });

        // Open share links in popup windows
        document.querySelectorAll('.share-link[target="_blank"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.href;
                const width = 600;
                const height = 400;
                const left = (screen.width - width) / 2;
                const top = (screen.height - height) / 2;
                
                window.open(url, 'share', `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`);
            });
        });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
