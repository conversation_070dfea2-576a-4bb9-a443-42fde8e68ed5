# Image Requirements for Arkify-Inspired Design

This document outlines the image requirements for the new Arkify-inspired design implementation.

## Required Images

### About Section
- `about-image-01.jpg` - First about image (existing: about-image.jpg can be used)
- `about-image-02.jpg` - Second about image for dual layout
- Recommended size: 600x400px minimum

### Featured Projects
- `projects/project-main01.jpg` - Main featured project image
- `projects/project-02.jpg` - Secondary project image
- `projects/project-03.jpg` - Secondary project image  
- `projects/project-04.jpg` - Secondary project image
- Recommended size: 800x600px for main, 400x300px for secondary

### Services Section
- `service.jpg` - Main service section image
- Recommended size: 600x450px

### Statistics/Logo Section
- `logos/logo-01.svg` - Client logo 1
- `logos/logo-02.svg` - Client logo 2
- `logos/logo-03.svg` - Client logo 3
- `logos/logo-04.svg` - Client logo 4
- `logos/logo-05.svg` - Client logo 5
- `logos/logo-06.svg` - Client logo 6
- Recommended: SVG format, monochrome for filtering

### Testimonials
- `testimonials/testimonial-01.jpg` - Client photo
- `testimonials/testimonial-02.jpg` - Client photo (if multiple testimonials)
- Recommended size: 300x300px, square format

### Blog/Articles
- `blog/blog-thumb01.jpg` - Featured article thumbnail
- `blog/blog-thumb02.jpg` - Article thumbnail 2
- `blog/blog-thumb03.jpg` - Article thumbnail 3
- `blog/blog-thumb04.jpg` - Article thumbnail 4
- Recommended size: 400x250px

## Image Guidelines

### Style
- Professional architectural photography
- Clean, modern aesthetic
- High contrast and sharp details
- Consistent color grading

### Technical Requirements
- Format: JPG for photos, SVG for logos/icons
- Quality: High resolution for retina displays
- Optimization: Compressed for web performance
- Alt text: Descriptive for accessibility

### Color Scheme
- Primary: Deep Charcoal (#1A1A1A)
- Secondary: Light gray backgrounds (#F5F5F5)
- Accent: Burnt Orange (#E67E22)
- Neutral: Grays and whites
- Supporting: Natural wood tones, concrete textures

## Fallback Images
If specific images are not available, the design will gracefully fall back to:
- Existing project images
- Placeholder content
- Default testimonial structure
- Generic architectural imagery

## Implementation Notes
- All images should be placed in their respective directories
- Update the PHP variables in index.php if using dynamic content
- Ensure proper alt attributes for accessibility
- Consider lazy loading for performance optimization
