<?php
/**
 * News Details Page - Individual Article/Blog Post
 * Complete single-page template for news content display
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get news slug from URL
$slug = isset($_GET['slug']) ? sanitizeInput($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: ' . siteUrl('news'));
    exit;
}

// Fetch news article from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND active = 1");
$stmt->execute([$slug]);
$article = $stmt->fetch();

// If no article found in database, create demo content
if (!$article) {
    $demo_articles = [
        'smart-building-technologies' => [
            'id' => 1,
            'title' => 'Smart Building Technologies: The Integration Revolution',
            'slug' => 'smart-building-technologies',
            'excerpt' => 'Exploring how IoT, AI, and automation are transforming modern buildings into intelligent, responsive environments.',
            'category' => 'Technology',
            'published_at' => '2023-12-10 10:00:00',
            'content' => '<p>The architecture industry is experiencing a digital transformation that\'s reshaping how we design, construct, and manage buildings. Smart building technologies are no longer futuristic concepts—they\'re becoming essential components of modern architectural design.</p>

<p>Today\'s intelligent buildings utilize Internet of Things (IoT) sensors, artificial intelligence, and advanced automation systems to create responsive environments that adapt to occupant needs while optimizing energy efficiency and operational costs.</p>

<h2>Key Smart Building Technologies</h2>

<h3>IoT Sensors and Monitoring</h3>
<p>Modern buildings are equipped with thousands of sensors that continuously monitor everything from air quality and temperature to occupancy patterns and energy consumption. These sensors provide real-time data that enables building systems to make intelligent decisions automatically.</p>

<h3>AI-Powered Building Management</h3>
<p>Artificial intelligence analyzes the vast amounts of data collected by IoT sensors to predict patterns, optimize systems, and prevent issues before they occur. AI can automatically adjust lighting, heating, and cooling systems based on occupancy patterns and weather conditions.</p>

<h3>Advanced Automation Systems</h3>
<p>Modern building automation goes beyond simple HVAC control. Today\'s systems can integrate lighting, security, elevators, and even window shades to create a seamless, intelligent environment that responds to both occupant needs and energy efficiency goals.</p>

<h2>Benefits of Smart Building Integration</h2>

<p><strong>Energy Efficiency:</strong> Smart buildings typically reduce energy consumption by 20-30% through intelligent automation and optimization of building systems.</p>

<p><strong>Enhanced Comfort:</strong> Automated systems maintain optimal environmental conditions while adapting to individual preferences and usage patterns.</p>

<p><strong>Predictive Maintenance:</strong> AI systems can predict equipment failures before they occur, reducing downtime and maintenance costs.</p>

<p><strong>Improved Security:</strong> Integrated security systems with AI-powered analytics provide enhanced protection and rapid response capabilities.</p>

<h2>The Future of Smart Buildings</h2>

<p>As technology continues to evolve, we can expect even more sophisticated integration between building systems and occupant needs. Machine learning algorithms will become more adept at predicting and adapting to human behavior, while new technologies like 5G networks will enable even faster response times and more sophisticated automation.</p>

<p>At Monolith Design, we\'re at the forefront of this smart building revolution, incorporating the latest technologies into our architectural designs to create buildings that are not just beautiful and functional, but truly intelligent.</p>',
            'featured_image' => 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200&h=600&fit=crop',
            'author' => 'Sarah Chen',
            'created_at' => '2023-12-10 10:00:00'
        ],
        'sustainable-architecture-trends' => [
            'id' => 2,
            'title' => 'The Future of Sustainable Architecture: Leading the Green Revolution',
            'slug' => 'sustainable-architecture-trends',
            'excerpt' => 'Sustainable architecture has emerged as more than just a trend—it\'s become a necessity for the future of our planet.',
            'category' => 'Sustainability',
            'published_at' => '2023-12-15 09:00:00',
            'content' => '<p>As the construction industry faces increasing pressure to reduce its environmental impact, sustainable architecture has emerged as more than just a trend—it\'s become a necessity. The built environment accounts for nearly 40% of global carbon emissions, making the transformation to green building practices crucial for addressing climate change.</p>

<h2>The Evolution of Sustainable Design</h2>

<p>Sustainable architecture has evolved significantly over the past decade. What once focused primarily on energy efficiency has expanded to encompass a holistic approach that considers the entire lifecycle of a building, from material sourcing to end-of-life disposal.</p>

<h3>Material Innovation</h3>
<p>The development of sustainable building materials has revolutionized how we approach construction. Cross-laminated timber (CLT) offers a renewable alternative to steel and concrete, while recycled materials and bio-based composites reduce the environmental footprint of new construction.</p>

<h3>Energy Systems Integration</h3>
<p>Modern sustainable buildings integrate renewable energy systems seamlessly into their design. Solar panels, geothermal systems, and wind power are no longer afterthoughts but integral components of the architectural vision.</p>

<h2>Beyond LEED: The Next Generation of Green Building</h2>

<p>While LEED certification remains an important standard, the future of sustainable architecture goes beyond traditional green building metrics. New approaches focus on:</p>

<ul>
<li><strong>Regenerative Design:</strong> Buildings that give back more to the environment than they consume</li>
<li><strong>Biophilic Architecture:</strong> Integrating natural elements to improve occupant health and well-being</li>
<li><strong>Circular Economy Principles:</strong> Designing for disassembly and material reuse</li>
<li><strong>Climate Resilience:</strong> Buildings designed to adapt to changing environmental conditions</li>
</ul>

<h2>Technology Enabling Sustainability</h2>

<p>Advanced technologies are making sustainable design more achievable and measurable. Building Information Modeling (BIM) allows architects to analyze environmental performance during the design phase, while IoT sensors provide real-time feedback on building performance.</p>

<h3>Smart Glass and Adaptive Facades</h3>
<p>Electrochromic glass and adaptive building skins automatically adjust to optimize natural light and thermal performance, reducing energy consumption while maintaining occupant comfort.</p>

<h3>AI-Optimized Building Systems</h3>
<p>Artificial intelligence analyzes building performance data to continuously optimize energy systems, often achieving efficiency improvements of 20-30% over traditional building management systems.</p>

<h2>The Business Case for Sustainable Architecture</h2>

<p>Sustainable buildings offer compelling economic benefits that extend far beyond reduced utility costs:</p>

<p><strong>Increased Property Values:</strong> Green buildings typically command 7-10% higher sale prices and rental rates compared to conventional buildings.</p>

<p><strong>Lower Operating Costs:</strong> Energy-efficient systems and sustainable materials often result in 20-40% lower operating costs over the building\'s lifetime.</p>

<p><strong>Enhanced Productivity:</strong> Studies show that occupants of green buildings experience improved health, comfort, and productivity.</p>

<p><strong>Future-Proofing:</strong> Sustainable buildings are better positioned to meet evolving environmental regulations and market demands.</p>

<h2>Monolith Design\'s Commitment to Sustainability</h2>

<p>At Monolith Design, sustainability isn\'t just a service we offer—it\'s fundamental to how we approach every project. Our integrated design process considers environmental impact from the earliest conceptual stages through construction and occupancy.</p>

<p>We\'re proud to have achieved LEED Platinum certification on 80% of our recent projects and are currently piloting regenerative design principles on several upcoming developments. Our goal is not just to minimize environmental impact, but to create buildings that actively contribute to environmental restoration.</p>

<h2>Looking Ahead</h2>

<p>The future of sustainable architecture is bright, driven by technological innovation, regulatory support, and growing market demand. As we continue to push the boundaries of what\'s possible in green building design, we\'re not just creating better buildings—we\'re helping to create a more sustainable future for all.</p>',
            'featured_image' => 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=1200&h=600&fit=crop',
            'author' => 'Alexander Thompson',
            'created_at' => '2023-12-15 09:00:00'
        ],
        'urban-planning-community-design' => [
            'id' => 3,
            'title' => 'Reimagining Urban Spaces: Community-Centered Design',
            'slug' => 'urban-planning-community-design',
            'excerpt' => 'How modern urban planning prioritizes community needs, creating spaces that foster connection and quality of life.',
            'category' => 'Urban Planning',
            'published_at' => '2023-12-08 14:30:00',
            'content' => '<p>Urban planning is undergoing a fundamental shift from car-centric development to people-centered design. This transformation recognizes that successful cities are built around human-scale environments that foster community connection, economic opportunity, and environmental sustainability.</p>

<h2>The Human-Scale City</h2>

<p>Modern urban planning embraces the concept of the "15-minute city," where residents can access most daily needs within a short walk or bike ride. This approach reduces dependence on automobiles while creating vibrant, walkable neighborhoods that encourage community interaction.</p>

<h3>Mixed-Use Development</h3>
<p>Rather than segregating residential, commercial, and office spaces, contemporary urban design integrates these functions to create dynamic, 24-hour neighborhoods. Ground-floor retail activates streets while upper-floor residential and office uses ensure continuous activity throughout the day.</p>

<h3>Green Infrastructure</h3>
<p>Urban green spaces are no longer afterthoughts but integral components of city infrastructure. Parks, green corridors, and urban forests provide environmental benefits while creating spaces for community gathering and recreation.</p>

<h2>Community Engagement in Design</h2>

<p>Successful urban planning requires meaningful community participation from the earliest stages of development. Modern planning processes incorporate diverse voices and perspectives to ensure that new developments truly serve community needs.</p>

<h3>Participatory Design Workshops</h3>
<p>Community charrettes and design workshops allow residents to actively participate in shaping their neighborhoods. These collaborative processes often reveal insights and priorities that professional planners might otherwise overlook.</p>

<h3>Cultural Preservation and Innovation</h3>
<p>Thoughtful urban planning respects and preserves existing cultural assets while creating space for innovation and growth. This balance ensures that development enhances rather than displaces existing communities.</p>

<h2>Technology in Urban Planning</h2>

<p>Digital tools are revolutionizing how cities are planned and managed. Geographic Information Systems (GIS), data analytics, and virtual reality enable more informed decision-making and better community engagement.</p>

<h3>Smart City Integration</h3>
<p>IoT sensors and data analytics provide real-time insights into how urban spaces are used, enabling more responsive and adaptive planning decisions. This data-driven approach helps optimize everything from traffic flow to park programming.</p>

<h3>Virtual Reality Planning</h3>
<p>VR technology allows communities to experience proposed developments before they\'re built, facilitating more informed public input and reducing conflicts during the approval process.</p>

<h2>Transit-Oriented Development</h2>

<p>Public transportation forms the backbone of sustainable urban development. Transit-oriented design creates compact, walkable communities centered around high-quality public transit, reducing car dependence and supporting local businesses.</p>

<h3>Complete Streets Design</h3>
<p>Modern street design accommodates all users—pedestrians, cyclists, transit riders, and drivers—through careful attention to lane configuration, sidewalk design, and intersection treatment.</p>

<h2>Economic Equity in Development</h2>

<p>Community-centered urban planning addresses economic inequality through inclusive development strategies that provide opportunities for existing residents while attracting new investment.</p>

<p><strong>Affordable Housing Integration:</strong> Rather than concentrating affordable housing in specific areas, inclusive planning distributes affordable units throughout neighborhoods to promote economic diversity.</p>

<p><strong>Local Business Support:</strong> Planning policies can protect and support local businesses through zoning incentives, facade improvement programs, and small business incubation spaces.</p>

<p><strong>Workforce Development:</strong> Major developments can include training and employment opportunities for local residents, ensuring that community members benefit from neighborhood investment.</p>

<h2>Climate Resilience</h2>

<p>Urban planning increasingly addresses climate change adaptation and mitigation. Resilient communities are designed to handle extreme weather events while reducing overall carbon emissions.</p>

<h3>Flood Management</h3>
<p>Natural stormwater management systems, including bioswales and permeable pavement, reduce flood risk while creating attractive green spaces.</p>

<h3>Urban Heat Island Reduction</h3>
<p>Strategic tree planting, green roofs, and light-colored surfaces help reduce urban temperatures and improve air quality.</p>

<h2>Case Study: The Waterfront District Transformation</h2>

<p>Our recent work on the Waterfront District exemplifies community-centered urban planning principles. Through extensive community engagement, we developed a master plan that preserves the area\'s maritime heritage while creating new opportunities for housing, retail, and recreation.</p>

<p>Key features include:</p>
<ul>
<li>A continuous waterfront promenade connecting previously isolated areas</li>
<li>Mixed-income housing integrated throughout the development</li>
<li>A maritime museum and maker space celebrating local history</li>
<li>Green infrastructure managing stormwater while creating community gathering spaces</li>
<li>Transit connections linking the district to the broader metropolitan area</li>
</ul>

<h2>The Future of Urban Planning</h2>

<p>As cities continue to grow and evolve, the principles of community-centered design will become increasingly important. Future urban planning will need to balance density with livability, technology with human connection, and economic development with environmental sustainability.</p>

<p>At Monolith Design, we believe that successful cities are built from the ground up, with community needs and values at the center of every planning decision. By prioritizing people over cars, collaboration over top-down planning, and sustainability over short-term profits, we can create cities that truly serve all residents.</p>',
            'featured_image' => 'https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=1200&h=600&fit=crop',
            'author' => 'Marcus Rodriguez',
            'created_at' => '2023-12-08 14:30:00'
        ]
    ];
    
    if (isset($demo_articles[$slug])) {
        $article = $demo_articles[$slug];
    } else {
        header('Location: ' . siteUrl('404'));
        exit;
    }
}

// Get related articles (use demo articles for now)
$related_articles = [];
if (isset($demo_articles)) {
    $demo_related = [
        [
            'id' => 4,
            'title' => 'Revolutionary Construction Methods: Building the Future',
            'slug' => 'construction-innovation-methods',
            'excerpt' => 'From 3D printing to modular construction, discover cutting-edge techniques making building faster and more efficient.',
            'category' => 'Innovation',
            'published_at' => '2023-12-03 11:00:00',
            'featured_image' => 'https://images.unsplash.com/photo-1503387762-592deb58ef4e?w=600&h=400&fit=crop',
            'author' => 'David Kim',
            'created_at' => '2023-12-03 11:00:00'
        ],
        [
            'id' => 5,
            'title' => '2024 Interior Design Trends: Function Meets Beauty',
            'slug' => 'interior-design-trends-2024',
            'excerpt' => 'A comprehensive look at interior design trends focusing on sustainable materials and wellness-centered environments.',
            'category' => 'Design',
            'published_at' => '2023-11-30 16:00:00',
            'featured_image' => 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=600&h=400&fit=crop',
            'author' => 'Lisa Park',
            'created_at' => '2023-11-30 16:00:00'
        ],
        [
            'id' => 6,
            'title' => 'Monolith Design Wins Prestigious Architecture Award',
            'slug' => 'monolith-design-architecture-award',
            'excerpt' => 'We\'re honored to receive the AIA National Award for our Metropolitan Office Complex project.',
            'category' => 'Company News',
            'published_at' => '2023-11-28 09:00:00',
            'featured_image' => 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=600&h=400&fit=crop',
            'author' => 'Alexander Thompson',
            'created_at' => '2023-11-28 09:00:00'
        ]
    ];
    $related_articles = $demo_related;
} else {
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE id != ? AND active = 1 ORDER BY created_at DESC LIMIT 3");
    $stmt->execute([$article['id']]);
    $related_articles = $stmt->fetchAll();
}

// Get next and previous articles for navigation (demo navigation)
$next_article = false;
$prev_article = false;
if (isset($demo_articles)) {
    $article_keys = array_keys($demo_articles);
    $current_index = array_search($slug, $article_keys);
    
    if ($current_index !== false) {
        if ($current_index < count($article_keys) - 1) {
            $next_slug = $article_keys[$current_index + 1];
            $next_article = [
                'slug' => $next_slug,
                'title' => $demo_articles[$next_slug]['title']
            ];
        }
        if ($current_index > 0) {
            $prev_slug = $article_keys[$current_index - 1];
            $prev_article = [
                'slug' => $prev_slug,
                'title' => $demo_articles[$prev_slug]['title']
            ];
        }
    }
} else {
    $stmt = $db->prepare("SELECT id, slug, title FROM blog_posts WHERE id > ? AND active = 1 ORDER BY id ASC LIMIT 1");
    $stmt->execute([$article['id']]);
    $next_article = $stmt->fetch();

    $stmt = $db->prepare("SELECT id, slug, title FROM blog_posts WHERE id < ? AND active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute([$article['id']]);
    $prev_article = $stmt->fetch();
}

$pageTitle = $article['title'] . ' - ' . SITE_NAME;
$pageDescription = $article['excerpt'] ?: 'Read our latest insights on ' . $article['title'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:image" content="<?php echo $article['featured_image'] ?: themeUrl('images/logo.svg'); ?>">
    <meta property="og:url" content="<?php echo siteUrl('news/' . $article['slug']); ?>">
    <meta property="og:type" content="article">
    
    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($article['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="twitter:image" content="<?php echo $article['featured_image'] ?: themeUrl('images/logo.svg'); ?>">
    
    <!-- Article-specific styles -->
    <style>
        /* Breadcrumb Navigation */
        .breadcrumb-nav {
            background: #F8F9FA;
            padding: 1rem 0;
            font-size: 0.9rem;
        }
        
        .breadcrumb-nav .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6c757d;
        }
        
        .breadcrumb a {
            color: var(--accent-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .breadcrumb a:hover {
            color: #d66e1a;
            text-decoration: underline;
        }
        
        .breadcrumb-separator {
            margin: 0 0.5rem;
            color: #adb5bd;
        }
        
        /* Article Hero Section */
        .article-hero {
            height: 60vh;
            background-size: cover;
            background-position: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            min-height: 500px;
        }
        
        .article-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(rgba(26, 26, 26, 0.5), rgba(26, 26, 26, 0.7));
            z-index: 1;
        }
        
        .article-hero-content {
            position: relative;
            z-index: 2;
            max-width: 900px;
            padding: 2rem;
        }
        
        .article-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
            line-height: 1.2;
        }
        
        .article-category {
            display: inline-block;
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
        }
        
        /* Article Meta Information */
        .article-meta {
            background: #F8F9FA;
            padding: 2rem 0;
            border-bottom: 1px solid #E9ECEF;
        }
        
        .meta-info {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .meta-item strong {
            color: var(--text-color);
            font-weight: 600;
        }
        
        .meta-icon {
            width: 18px;
            height: 18px;
            fill: var(--accent-color);
        }
        
        /* Article Content */
        .article-content {
            padding: 4rem 0;
            background: white;
        }
        
        .article-body {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
            line-height: 1.8;
        }
        
        .article-body h2,
        .article-body h3,
        .article-body h4 {
            color: var(--text-color);
            margin: 2rem 0 1rem;
            font-weight: 600;
        }
        
        .article-body h2 {
            font-size: 1.8rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.5rem;
        }
        
        .article-body h3 {
            font-size: 1.4rem;
            color: var(--accent-color);
        }
        
        .article-body p {
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
            color: #333;
        }
        
        .article-body ul,
        .article-body ol {
            margin: 1.5rem 0;
            padding-left: 2rem;
        }
        
        .article-body li {
            margin-bottom: 0.5rem;
        }
        
        .article-body blockquote {
            background: #F8F9FA;
            border-left: 4px solid var(--accent-color);
            padding: 1.5rem;
            margin: 2rem 0;
            font-style: italic;
            font-size: 1.1rem;
        }
        
        .article-body img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 2rem 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        /* Social Share Buttons */
        .share-section {
            background: #F8F9FA;
            padding: 3rem 0;
            text-align: center;
        }
        
        .share-buttons {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .share-buttons h4 {
            color: var(--text-color);
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
        }
        
        .share-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .share-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .share-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .share-facebook { background: #1877f2; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        .share-email { background: #666; }
        
        /* Article Tags */
        .article-tags {
            padding: 3rem 0;
            background: white;
            border-top: 1px solid #E9ECEF;
        }
        
        .tags-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }
        
        .tags-container h4 {
            color: var(--text-color);
            margin-bottom: 1rem;
        }
        
        .tags-list {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .tag {
            display: inline-block;
            background: #E9ECEF;
            color: var(--text-color);
            padding: 0.4rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .tag:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-1px);
        }
        
        /* Related Articles */
        .related-articles {
            padding: 4rem 0;
            background: #F8F9FA;
        }
        
        .related-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .section-title {
            text-align: center;
            color: var(--text-color);
            font-size: 2rem;
            margin-bottom: 3rem;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .related-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .related-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .related-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .related-card-content {
            padding: 1.5rem;
        }
        
        .related-card h3 {
            color: var(--text-color);
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }
        
        .related-card h3 a {
            color: inherit;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .related-card h3 a:hover {
            color: var(--accent-color);
        }
        
        .related-card .category {
            color: var(--accent-color);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }
        
        .related-card .date {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* Navigation Buttons */
        .article-navigation {
            padding: 3rem 0;
            background: white;
            border-top: 1px solid #E9ECEF;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 2rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: color 0.3s ease;
            max-width: 300px;
        }
        
        .nav-link:hover {
            color: var(--accent-color);
        }
        
        .nav-link.prev {
            text-align: left;
        }
        
        .nav-link.next {
            text-align: right;
            flex-direction: row-reverse;
        }
        
        .nav-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.3rem;
        }
        
        .nav-title {
            font-weight: 600;
            line-height: 1.3;
        }
        
        .back-to-news {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--accent-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .back-to-news:hover {
            background: #d66e1a;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
            color: white;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .article-hero h1 {
                font-size: 2rem;
            }
            
            .article-hero-content {
                padding: 1rem;
            }
            
            .meta-info {
                flex-direction: column;
                gap: 1rem;
            }
            
            .share-links {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-container {
                flex-direction: column;
                text-align: center;
            }
            
            .related-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 480px) {
            .article-body {
                padding: 0 1rem;
            }
            
            .article-hero h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Breadcrumb Navigation -->
    <section class="breadcrumb-nav">
        <div class="container">
            <nav class="breadcrumb">
                <a href="<?php echo siteUrl(); ?>">Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo siteUrl('news'); ?>">News</a>
                <span class="breadcrumb-separator">›</span>
                <span><?php echo htmlspecialchars($article['title']); ?></span>
            </nav>
        </div>
    </section>

    <!-- Article Hero Section -->
    <section class="article-hero" style="background-image: url('<?php echo $article['featured_image'] ?: themeUrl('images/hero-bg-1.jpg'); ?>');">
        <div class="article-hero-content">
            <?php if ($article['category']): ?>
                <div class="article-category"><?php echo htmlspecialchars($article['category']); ?></div>
            <?php endif; ?>
            <h1><?php echo htmlspecialchars($article['title']); ?></h1>
            <?php if ($article['excerpt']): ?>
                <p style="font-size: 1.2rem; margin-top: 1rem; opacity: 0.9;"><?php echo htmlspecialchars($article['excerpt']); ?></p>
            <?php endif; ?>
        </div>
    </section>

    <!-- Article Meta Information -->
    <section class="article-meta">
        <div class="container">
            <div class="meta-info">
                <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H5V21H19V9Z"/>
                    </svg>
                    <span><strong>Published:</strong> <?php echo date('F j, Y', strtotime($article['published_at'])); ?></span>
                </div>
                <?php if ($article['author']): ?>
                <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24">
                        <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                    </svg>
                    <span><strong>Author:</strong> <?php echo htmlspecialchars($article['author']); ?></span>
                </div>
                <?php endif; ?>
                <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
                    </svg>
                    <span><strong>Read Time:</strong> <?php echo max(1, ceil(str_word_count(strip_tags($article['content'])) / 200)); ?> min read</span>
                </div>
                <?php if ($article['category']): ?>
                <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 24 24">
                        <path d="M17.63,5.84C17.27,5.33 16.67,5 16,5L5,5.01C3.9,5.01 3,5.9 3,7V17C3,18.1 3.9,19 5,19H16C16.67,19 17.27,18.67 17.63,18.16L22,12L17.63,5.84Z"/>
                    </svg>
                    <span><strong>Category:</strong> <?php echo htmlspecialchars($article['category']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Article Content -->
    <section class="article-content">
        <div class="container">
            <article class="article-body">
                <?php echo $article['content']; ?>
            </article>
        </div>
    </section>

    <!-- Article Tags -->
    <?php if (!empty($article['tags'])): ?>
    <section class="article-tags">
        <div class="container">
            <div class="tags-container">
                <h4>Article Tags</h4>
                <div class="tags-list">
                    <?php 
                    $tags = explode(',', $article['tags']);
                    foreach ($tags as $tag): 
                        $tag = trim($tag);
                        if (!empty($tag)):
                    ?>
                        <a href="<?php echo siteUrl('news?tag=' . urlencode($tag)); ?>" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                    <?php 
                        endif;
                    endforeach; 
                    ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Social Share Section -->
    <section class="share-section">
        <div class="container">
            <div class="share-buttons">
                <h4>Share This Article</h4>
                <div class="share-links">
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                       target="_blank" class="share-link share-facebook">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        Facebook
                    </a>
                    <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>&text=<?php echo urlencode($article['title']); ?>" 
                       target="_blank" class="share-link share-twitter">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        Twitter
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                       target="_blank" class="share-link share-linkedin">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn
                    </a>
                    <a href="mailto:?subject=<?php echo urlencode($article['title']); ?>&body=<?php echo urlencode('Check out this article: ' . siteUrl('news/' . $article['slug'])); ?>" 
                       class="share-link share-email">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 .02l-12 6.51v11.47h24v-11.47zm0 2.03l8.74 4.67h-17.48zm-10 6.47h20v9h-20z"/>
                        </svg>
                        Email
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Article Navigation -->
    <section class="article-navigation">
        <div class="container">
            <div class="nav-container">
                <?php if ($prev_article): ?>
                <a href="<?php echo siteUrl('news/' . $prev_article['slug']); ?>" class="nav-link prev">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z"/>
                    </svg>
                    <div>
                        <div class="nav-label">Previous Article</div>
                        <div class="nav-title"><?php echo htmlspecialchars($prev_article['title']); ?></div>
                    </div>
                </a>
                <?php endif; ?>
                
                <a href="<?php echo siteUrl('news'); ?>" class="back-to-news">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
                    </svg>
                    Back to News
                </a>
                
                <?php if ($next_article): ?>
                <a href="<?php echo siteUrl('news/' . $next_article['slug']); ?>" class="nav-link next">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"/>
                    </svg>
                    <div>
                        <div class="nav-label">Next Article</div>
                        <div class="nav-title"><?php echo htmlspecialchars($next_article['title']); ?></div>
                    </div>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Related Articles -->
    <?php if (!empty($related_articles)): ?>
    <section class="related-articles">
        <div class="container">
            <div class="related-container">
                <h2 class="section-title">Related Articles</h2>
                <div class="related-grid">
                    <?php foreach ($related_articles as $related): ?>
                    <div class="related-card">
                        <?php if ($related['featured_image']): ?>
                            <img src="<?php echo $related['featured_image']; ?>" alt="<?php echo htmlspecialchars($related['title']); ?>">
                        <?php else: ?>
                            <img src="<?php echo themeUrl('images/hero-bg-1.jpg'); ?>" alt="<?php echo htmlspecialchars($related['title']); ?>">
                        <?php endif; ?>
                        <div class="related-card-content">
                            <?php if ($related['category']): ?>
                                <div class="category"><?php echo htmlspecialchars($related['category']); ?></div>
                            <?php endif; ?>
                            <h3><a href="<?php echo siteUrl('news/' . $related['slug']); ?>"><?php echo htmlspecialchars($related['title']); ?></a></h3>
                            <div class="date"><?php echo date('F j, Y', strtotime($related['published_at'])); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>

    <!-- JavaScript for smooth scrolling and interactions -->
    <script>
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add copy link functionality
        function copyToClipboard() {
            navigator.clipboard.writeText(window.location.href).then(function() {
                // Show success message (you can customize this)
                const btn = event.target.closest('.share-link');
                const originalText = btn.innerHTML;
                btn.innerHTML = '✓ Copied!';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                }, 2000);
            });
        }

        // Open share links in popup windows
        document.querySelectorAll('.share-link[target="_blank"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.href;
                const width = 600;
                const height = 400;
                const left = (screen.width - width) / 2;
                const top = (screen.height - height) / 2;
                
                window.open(url, 'share', `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`);
            });
        });
    </script>
</body>
</html>
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .meta-item strong {
            color: var(--accent-color);
        }
        
        .article-content {
            padding: 4rem 0;
        }
        
        .article-body {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
        }
        
        .article-body h2,
        .article-body h3,
        .article-body h4 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .article-body h2 {
            font-size: 1.8rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.5rem;
        }
        
        .article-body h3 {
            font-size: 1.4rem;
            color: var(--accent-color);
        }
        
        .article-body p {
            margin-bottom: 1.5rem;
        }
        
        .article-body ul,
        .article-body ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
        }
        
        .article-body li {
            margin-bottom: 0.5rem;
        }
        
        .article-body blockquote {
            background: #F8F9FA;
            border-left: 4px solid var(--accent-color);
            padding: 1.5rem;
            margin: 2rem 0;
            font-style: italic;
        }
        
        .article-tags {
            background: #F8F9FA;
            padding: 2rem 0;
            border-top: 1px solid #E9ECEF;
        }
        
        .tags-list {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .tag {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .tag:hover {
            background: #d35400;
            color: white;
        }
        
        .related-articles {
            padding: 4rem 0;
            background: #F5F5F5;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .related-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .related-card:hover {
            transform: translateY(-5px);
        }
        
        .related-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .related-card-content {
            padding: 1.5rem;
        }
        
        .related-card h3 {
            margin-bottom: 0.5rem;
            color: var(--text-color);
            font-size: 1.2rem;
        }
        
        .related-card .category {
            color: var(--accent-color);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .related-card .date {
            color: #666;
            font-size: 0.85rem;
            margin-top: 1rem;
        }
        
        .share-buttons {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid #E9ECEF;
            border-bottom: 1px solid #E9ECEF;
        }
        
        .share-buttons h4 {
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .share-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        
        .share-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            transition: transform 0.3s ease;
        }
        
        .share-link:hover {
            transform: scale(1.1);
            color: white;
        }
        
        .share-facebook { background: #1877f2; }
        .share-twitter { background: #1da1f2; }
        .share-linkedin { background: #0077b5; }
        .share-email { background: #666; }
        
        @media (max-width: 768px) {
            .article-hero h1 {
                font-size: 1.8rem;
            }
            
            .meta-info {
                gap: 1rem;
                justify-content: flex-start;
            }
            
            .article-body {
                font-size: 1rem;
            }
            
            .share-links {
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Main Content -->
    <main>
        <!-- Article Hero -->
        <section class="article-hero" style="background-image: url('<?php echo $article['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200&h=600&fit=crop'; ?>');">
            <div class="article-hero-content">
                <h1><?php echo htmlspecialchars($article['title']); ?></h1>
                <p class="lead"><?php echo htmlspecialchars($article['excerpt']); ?></p>
            </div>
        </section>

        <!-- Article Meta -->
        <section class="article-meta">
            <div class="container">
                <div class="meta-info">
                    <div class="meta-item">
                        <strong>Author:</strong> <?php echo htmlspecialchars($article['author'] ?: 'Monolith Design Team'); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Published:</strong> <?php echo date('F j, Y', strtotime($article['published_at'])); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Category:</strong> <?php echo htmlspecialchars($article['category']); ?>
                    </div>
                    <div class="meta-item">
                        <strong>Reading Time:</strong> <?php echo ceil(str_word_count(strip_tags($article['content'])) / 200); ?> min
                    </div>
                </div>
            </div>
        </section>

        <!-- Article Content -->
        <section class="article-content">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="article-body">
                            <?php echo $article['content']; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Share Buttons -->
        <section class="share-buttons">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <h4>Share This Article</h4>
                        <div class="share-links">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-facebook" target="_blank" rel="noopener">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>&text=<?php echo urlencode($article['title']); ?>" 
                               class="share-link share-twitter" target="_blank" rel="noopener">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-linkedin" target="_blank" rel="noopener">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="mailto:?subject=<?php echo urlencode($article['title']); ?>&body=<?php echo urlencode('Check out this article: ' . siteUrl('news/' . $article['slug'])); ?>" 
                               class="share-link share-email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Article Tags -->
        <?php if (!empty($article['tags'])): ?>
        <section class="article-tags">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h4>Tags</h4>
                        <div class="tags-list">
                            <?php 
                            $tags = explode(',', $article['tags']);
                            foreach ($tags as $tag): 
                                $tag = trim($tag);
                                if (!empty($tag)):
                            ?>
                            <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Related Articles -->
        <?php if (!empty($related_articles)): ?>
        <section class="related-articles">
            <div class="container">
                <div class="section-header text-center">
                    <h2>Related Articles</h2>
                    <p>More insights and updates from our team</p>
                </div>
                
                <div class="related-grid">
                    <?php foreach ($related_articles as $related): ?>
                    <article class="related-card">
                        <img src="<?php echo $related['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=200&fit=crop'; ?>" 
                             alt="<?php echo htmlspecialchars($related['title']); ?>" 
                             class="loading">
                        <div class="related-card-content">
                            <div class="category"><?php echo htmlspecialchars($related['category']); ?></div>
                            <h3><?php echo htmlspecialchars($related['title']); ?></h3>
                            <p><?php echo htmlspecialchars(substr($related['excerpt'], 0, 120)) . '...'; ?></p>
                            <div class="date"><?php echo date('F j, Y', strtotime($related['published_at'])); ?></div>
                            <a href="<?php echo siteUrl('news/' . $related['slug']); ?>" class="btn btn-outline" style="margin-top: 1rem;">Read More</a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Newsletter CTA -->
        <section class="newsletter-cta" style="background: var(--primary-color); color: white; padding: 4rem 0;">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2>Stay Updated</h2>
                        <p class="lead">Get the latest insights on architecture and design trends delivered to your inbox.</p>
                        <form class="newsletter-form" style="display: flex; gap: 1rem; max-width: 400px; margin: 2rem auto 0;">
                            <input type="email" placeholder="Your email address" style="flex: 1; padding: 0.75rem; border: none; border-radius: 4px;">
                            <button type="submit" class="btn" style="background: var(--accent-color); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px;">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Links -->
        <section style="padding: 2rem 0; background: #F8F9FA;">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <a href="<?php echo siteUrl('news'); ?>" class="btn btn-outline">← Back to News</a>
                            <a href="<?php echo siteUrl('contact'); ?>" class="btn btn-primary">Get In Touch</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- Scripts -->
    <script src="<?php echo themeUrl('js/main.js'); ?>"></script>
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
    
    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</body>
</html>
