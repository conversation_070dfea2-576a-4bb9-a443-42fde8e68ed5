<?php
/**
 * News/Blog Page - Latest Updates and Insights
 * Features image-based articles instead of icons
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Fetch blog posts from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE status = 'published' ORDER BY published_at DESC");
$stmt->execute();
$blog_posts = $stmt->fetchAll();

// Get featured article (latest post)
$featured_article = !empty($blog_posts) ? $blog_posts[0] : null;

// Get other articles (excluding featured)
$other_articles = array_slice($blog_posts, 1);

$pageTitle = 'News & Insights - Latest from Monolith Design';
$pageDescription = 'Stay updated with the latest news, insights, and trends in architecture and design from our expert team at Monolith Design.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- News Page Specific CSS -->
    <style>
        /* ===== NEWS PAGE STYLES ===== */
        .news-hero {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            padding: 8rem 0 6rem;
            position: relative;
            overflow: hidden;
        }
        
        .news-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(230, 126, 34, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(230, 126, 34, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .news-hero-content {
            position: relative;
            z-index: 1;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .news-hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            color: var(--accent-color);
        }
        
        .news-hero p {
            font-size: 1.3rem;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }
        
        /* Featured Article */
        .featured-article {
            padding: 8rem 0;
            background: white;
        }
        
        .featured-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6rem;
            align-items: center;
        }
        
        .featured-image {
            position: relative;
        }
        
        .featured-image img {
            width: 100%;
            border-radius: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .featured-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .featured-info {
            padding-left: 2rem;
        }
        
        .featured-category {
            color: var(--accent-color);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
        }
        
        .featured-info h2 {
            color: var(--text-color);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .featured-meta {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .featured-author {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .featured-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .read-more-btn {
            background: var(--accent-color);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s ease;
            display: inline-block;
        }
        
        .read-more-btn:hover {
            background: #d4711d;
            color: white;
            text-decoration: none;
        }
        
        /* News Grid */
        .news-grid {
            padding: 8rem 0;
            background: #f8f9fa;
        }
        
        .news-grid h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 4rem;
            color: var(--text-color);
        }
        
        .news-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 3rem;
        }
        
        .news-card {
            background: white;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .news-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }
        
        .news-image {
            height: 250px;
            overflow: hidden;
            position: relative;
        }
        
        .news-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .news-card:hover .news-image img {
            transform: scale(1.1);
        }
        
        .news-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .news-content {
            padding: 2rem;
        }
        
        .news-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            color: #666;
            font-size: 0.9rem;
        }
        
        .news-title {
            font-size: 1.4rem;
            margin-bottom: 1rem;
            color: var(--text-color);
            line-height: 1.3;
        }
        
        .news-title a {
            color: inherit;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .news-title a:hover {
            color: var(--accent-color);
        }
        
        .news-excerpt {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .news-link {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        
        .news-link:hover {
            color: #d4711d;
            text-decoration: none;
        }
        
        /* Newsletter Section */
        .newsletter {
            padding: 6rem 0;
            background: var(--text-color);
            color: white;
            text-align: center;
        }
        
        .newsletter h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--accent-color);
        }
        
        .newsletter p {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .newsletter-form {
            display: flex;
            max-width: 500px;
            margin: 0 auto;
            gap: 0;
        }
        
        .newsletter-input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: none;
            font-size: 1rem;
            outline: none;
        }
        
        .newsletter-btn {
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .newsletter-btn:hover {
            background: #d4711d;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .featured-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }
            
            .featured-info {
                padding-left: 0;
            }
            
            .news-hero h1 {
                font-size: 2.5rem;
            }
            
            .featured-info h2 {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 768px) {
            .news-hero {
                padding: 6rem 0 4rem;
            }
            
            .featured-article,
            .news-grid,
            .newsletter {
                padding: 4rem 0;
            }
            
            .news-container {
                grid-template-columns: 1fr;
            }
            
            .newsletter-form {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- News Hero Section -->
    <section class="news-hero">
        <div class="container">
            <div class="news-hero-content">
                <h1>News & Insights</h1>
                <p>Stay connected with the latest developments in architecture, design trends, and company updates. Our expert insights and industry analysis keep you informed about the evolving world of architectural design and construction.</p>
            </div>
        </div>
    </section>

    <!-- Featured Article -->
    <?php if ($featured_article): ?>
    <section class="featured-article">
        <div class="container">
            <div class="featured-content">
                <div class="featured-image">
                    <img src="<?php echo $featured_article['featured_image'] ?: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800&h=600&fit=crop'; ?>" alt="<?php echo htmlspecialchars($featured_article['title']); ?>">
                </div>
                <div class="featured-info">
                    <div class="featured-category"><?php echo htmlspecialchars($featured_article['category']); ?></div>
                    <h2><?php echo htmlspecialchars($featured_article['title']); ?></h2>
                    <div class="featured-meta">
                        <div class="featured-author">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                            <span><?php echo htmlspecialchars($featured_article['author']); ?></span>
                        </div>
                        <div class="featured-date">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                            </svg>
                            <span><?php echo formatDate($featured_article['published_at'] ?: $featured_article['created_at']); ?></span>
                        </div>
                    </div>
                    <div class="featured-description">
                        <p><?php echo htmlspecialchars($featured_article['excerpt']); ?></p>
                    </div>
                    <a href="<?php echo siteUrl('news/' . $featured_article['slug']); ?>" class="read-more-btn">Read Full Article</a>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- News Grid -->
    <section class="news-grid">
        <div class="container">
            <h2>Latest News & Updates</h2>
            <div class="news-container">
                <?php if (!empty($other_articles)): ?>
                    <?php foreach ($other_articles as $article): ?>
                    <article class="news-card">
                        <div class="news-image">
                            <img src="<?php echo $article['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=600&h=400&fit=crop'; ?>" alt="<?php echo htmlspecialchars($article['title']); ?>">
                            <div class="news-badge"><?php echo htmlspecialchars($article['category']); ?></div>
                        </div>
                        <div class="news-content">
                            <div class="news-meta">
                                <span><?php echo htmlspecialchars($article['author']); ?></span>
                                <span>•</span>
                                <span><?php echo formatDate($article['published_at'] ?: $article['created_at']); ?></span>
                            </div>
                            <h3 class="news-title">
                                <a href="<?php echo siteUrl('news/' . $article['slug']); ?>"><?php echo htmlspecialchars($article['title']); ?></a>
                            </h3>
                            <p class="news-excerpt"><?php echo htmlspecialchars($article['excerpt']); ?></p>
                            <a href="<?php echo siteUrl('news/' . $article['slug']); ?>" class="news-link">Read More →</a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p style="text-align: center; color: #666; padding: 2rem;">No news articles found. Check back soon for updates!</p>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter">
        <div class="container">
            <h2>Stay Updated</h2>
            <p>Subscribe to our newsletter for the latest insights on architecture, design trends, and company updates delivered directly to your inbox.</p>
            <form class="newsletter-form">
                <input type="email" class="newsletter-input" placeholder="Enter your email address" required>
                <button type="submit" class="newsletter-btn">Subscribe</button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
    
    <!-- Newsletter Form JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const newsletterForm = document.querySelector('.newsletter-form');
            
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = this.querySelector('.newsletter-input').value;
                
                if (email) {
                    alert('Thank you for subscribing! You will receive our latest updates.');
                    this.querySelector('.newsletter-input').value = '';
                } else {
                    alert('Please enter a valid email address.');
                }
            });
        });
    </script>
</body>
</html>
