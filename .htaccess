# Monolith Design Co. - Production Ready .htaccess
# ==================================================

# Enable URL Rewriting
RewriteEngine On

# Security Protection
# ===================

# Block access to sensitive files
RewriteRule ^(config\.php|database\.sql|\.git|\.gitignore|composer\.)$ - [F,L]

# Clean URL Routing
# =================

# Specific page routing (must come before general rules)
RewriteRule ^services/?$ services.php [NC,L]
RewriteRule ^service/([^/]+)/?$ service-details.php?slug=$1 [NC,L]
RewriteRule ^projects/?$ projects.php [NC,L]
RewriteRule ^project/([^/]+)/?$ project-details.php?slug=$1 [NC,L]
RewriteRule ^contact/?$ contact.php [NC,L]
RewriteRule ^about/?$ about.php [NC,L]
RewriteRule ^team/?$ team.php [NC,L]
RewriteRule ^news/?$ news.php [NC,L]
RewriteRule ^blog/?$ news.php [NC,L]
RewriteRule ^blog/([^/]+)/?$ news-detail.php?slug=$1 [NC,L]
RewriteRule ^news/([^/]+)/?$ news-detail.php?slug=$1 [NC,L]

# Remove .php extension from URLs (general rule)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirect .php to clean URL
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1 [NC,L,R=301]
RewriteRule ^admin/?$ admin/index.php [NC,L]

# Security Headers
# ================
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # HSTS for HTTPS (uncomment for production HTTPS)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
</IfModule>

# Performance Optimization
# ========================

# GZIP Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE font/truetype
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # Fonts
    ExpiresByType font/truetype "access plus 1 year"
    ExpiresByType font/opentype "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    
    # Documents
    ExpiresByType application/pdf "access plus 1 month"
    
    # Default
    ExpiresDefault "access plus 1 week"
</IfModule>

# File Size Limits
# =================
# 10MB upload limit
LimitRequestBody 10485760

# PHP Settings (if allowed)
# =========================
<IfModule mod_php7.c>
    php_value upload_max_filesize 5M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
</IfModule>

# Error Pages
# ===========
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Directory Indexes
# =================
DirectoryIndex index.php index.html

# File Protection
# ===============
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>
