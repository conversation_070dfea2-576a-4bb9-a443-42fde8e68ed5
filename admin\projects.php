<?php
/**
 * Projects Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_project':
                $title = sanitizeInput($_POST['title']);
                $slug = createSlug($_POST['slug'] ?: $title);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content']; // Keep HTML formatting
                $category = sanitizeInput($_POST['category']);
                $client = sanitizeInput($_POST['client']);
                $location = sanitizeInput($_POST['location']);
                $year = sanitizeInput($_POST['year']);
                $status = sanitizeInput($_POST['status']);
                $featured_image = '';
                $gallery_images = '';
                
                // Handle featured image upload
                if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                    $upload = uploadFile($_FILES['featured_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload['success']) {
                        $featured_image = $upload['url'];
                    } else {
                        $error = 'Error uploading featured image: ' . $upload['message'];
                    }
                }
                
                // Handle gallery images upload
                $gallery_urls = [];
                if (isset($_FILES['gallery_images']) && is_array($_FILES['gallery_images']['name'])) {
                    for ($i = 0; $i < count($_FILES['gallery_images']['name']); $i++) {
                        if ($_FILES['gallery_images']['error'][$i] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['gallery_images']['name'][$i],
                                'type' => $_FILES['gallery_images']['type'][$i],
                                'tmp_name' => $_FILES['gallery_images']['tmp_name'][$i],
                                'error' => $_FILES['gallery_images']['error'][$i],
                                'size' => $_FILES['gallery_images']['size'][$i]
                            ];
                            $upload = uploadFile($file, ['jpg', 'jpeg', 'png', 'webp']);
                            if ($upload['success']) {
                                $gallery_urls[] = $upload['url'];
                            }
                        }
                    }
                }
                $gallery_images = implode(',', $gallery_urls);
                
                if (empty($error)) {
                    $stmt = $db->prepare("INSERT INTO projects (title, slug, description, content, category, client, location, year, status, featured_image, gallery_images, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
                    
                    if ($stmt->execute([$title, $slug, $description, $content, $category, $client, $location, $year, $status, $featured_image, $gallery_images])) {
                        $message = 'Project added successfully!';
                    } else {
                        $error = 'Error adding project to database.';
                    }
                }
                break;
                
            case 'update_project':
                $id = (int)$_POST['id'];
                $title = sanitizeInput($_POST['title']);
                $slug = createSlug($_POST['slug'] ?: $title);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content'];
                $category = sanitizeInput($_POST['category']);
                $client = sanitizeInput($_POST['client']);
                $location = sanitizeInput($_POST['location']);
                $year = sanitizeInput($_POST['year']);
                $status = sanitizeInput($_POST['status']);
                
                // Handle featured image upload
                $featured_image_sql = '';
                $params = [$title, $slug, $description, $content, $category, $client, $location, $year, $status];
                
                if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                    $upload = uploadFile($_FILES['featured_image'], ['jpg', 'jpeg', 'png', 'webp']);
                    if ($upload['success']) {
                        $featured_image_sql = ', featured_image = ?';
                        $params[] = $upload['url'];
                    }
                }
                
                // Handle gallery images upload
                $gallery_image_sql = '';
                if (isset($_FILES['gallery_images']) && is_array($_FILES['gallery_images']['name'])) {
                    $gallery_urls = [];
                    for ($i = 0; $i < count($_FILES['gallery_images']['name']); $i++) {
                        if ($_FILES['gallery_images']['error'][$i] === UPLOAD_ERR_OK) {
                            $file = [
                                'name' => $_FILES['gallery_images']['name'][$i],
                                'type' => $_FILES['gallery_images']['type'][$i],
                                'tmp_name' => $_FILES['gallery_images']['tmp_name'][$i],
                                'error' => $_FILES['gallery_images']['error'][$i],
                                'size' => $_FILES['gallery_images']['size'][$i]
                            ];
                            $upload = uploadFile($file, ['jpg', 'jpeg', 'png', 'webp']);
                            if ($upload['success']) {
                                $gallery_urls[] = $upload['url'];
                            }
                        }
                    }
                    if (!empty($gallery_urls)) {
                        $gallery_image_sql = ', gallery_images = ?';
                        $params[] = implode(',', $gallery_urls);
                    }
                }
                
                $params[] = $id;
                $stmt = $db->prepare("UPDATE projects SET title = ?, slug = ?, description = ?, content = ?, category = ?, client = ?, location = ?, year = ?, status = ?, updated_at = NOW(){$featured_image_sql}{$gallery_image_sql} WHERE id = ?");
                
                if ($stmt->execute($params)) {
                    $message = 'Project updated successfully!';
                } else {
                    $error = 'Error updating project.';
                }
                break;
                
            case 'delete_project':
                $id = (int)$_POST['id'];
                $stmt = $db->prepare("DELETE FROM projects WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'Project deleted successfully!';
                } else {
                    $error = 'Error deleting project.';
                }
                break;
        }
    }
}

// Get projects for listing
$projects = [];
$stmt = $db->prepare("SELECT * FROM projects ORDER BY year DESC, created_at DESC");
$stmt->execute();
$projects = $stmt->fetchAll();

// Get project for editing if requested
$edit_project = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $stmt = $db->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_project = $stmt->fetch();
}

// Helper function to create URL-friendly slug
function createSlug($string) {
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $string)));
    return trim($slug, '-');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects Management - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .admin-header {
            background: #1A1A1A;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav {
            background: #2c3e50;
            padding: 1rem 2rem;
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            margin-right: 2rem;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #E67E22;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-card-header {
            background: #34495e;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-card-body {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }
        
        input[type="text"],
        input[type="number"],
        textarea,
        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus,
        input[type="number"]:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #E67E22;
        }
        
        textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .content-editor {
            min-height: 300px;
        }
        
        .btn {
            background: #E67E22;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #d35400;
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .message {
            background: #fdf6f0;
            color: #E67E22;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .error {
            background: #fadbd8;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .projects-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .projects-table th,
        .projects-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .projects-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .projects-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-ongoing {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-planned {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .image-preview {
            max-width: 150px;
            max-height: 100px;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        
        .gallery-preview {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 0.5rem;
        }
        
        .gallery-preview img {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .project-form {
            display: none;
        }
        
        .project-form.active {
            display: block;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .file-info {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .projects-table {
                font-size: 0.9rem;
            }
            
            .projects-table th,
            .projects-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Projects Management</h1>
        <div>
            <a href="<?php echo siteUrl('projects'); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Projects</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="services.php">Services</a>
        <a href="projects.php" class="active">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <!-- Add/Edit Project Form -->
        <div class="admin-card">
            <div class="admin-card-header">
                <span><?php echo $edit_project ? 'Edit Project' : 'Add New Project'; ?></span>
                <button type="button" class="btn btn-secondary btn-small" onclick="toggleForm()">
                    <?php echo $edit_project ? 'Cancel Edit' : 'Add New Project'; ?>
                </button>
            </div>
            
            <div class="admin-card-body">
                <form method="POST" enctype="multipart/form-data" class="project-form <?php echo $edit_project ? 'active' : ''; ?>" id="projectForm">
                    <input type="hidden" name="action" value="<?php echo $edit_project ? 'update_project' : 'add_project'; ?>">
                    <?php if ($edit_project): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_project['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="title">Project Title *</label>
                            <input type="text" id="title" name="title" required 
                                   value="<?php echo $edit_project ? htmlspecialchars($edit_project['title']) : ''; ?>"
                                   onkeyup="generateSlug()">
                        </div>
                        
                        <div class="form-group">
                            <label for="slug">URL Slug</label>
                            <input type="text" id="slug" name="slug" 
                                   value="<?php echo $edit_project ? htmlspecialchars($edit_project['slug']) : ''; ?>"
                                   placeholder="Auto-generated from title">
                            <small style="color: #666; display: block; margin-top: 0.25rem;">Leave empty to auto-generate from title</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Commercial" <?php echo ($edit_project && $edit_project['category'] === 'Commercial') ? 'selected' : ''; ?>>Commercial</option>
                                <option value="Residential" <?php echo ($edit_project && $edit_project['category'] === 'Residential') ? 'selected' : ''; ?>>Residential</option>
                                <option value="Industrial" <?php echo ($edit_project && $edit_project['category'] === 'Industrial') ? 'selected' : ''; ?>>Industrial</option>
                                <option value="Infrastructure" <?php echo ($edit_project && $edit_project['category'] === 'Infrastructure') ? 'selected' : ''; ?>>Infrastructure</option>
                                <option value="Mixed-Use" <?php echo ($edit_project && $edit_project['category'] === 'Mixed-Use') ? 'selected' : ''; ?>>Mixed-Use</option>
                                <option value="Renovation" <?php echo ($edit_project && $edit_project['category'] === 'Renovation') ? 'selected' : ''; ?>>Renovation</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" required>
                                <option value="completed" <?php echo ($edit_project && $edit_project['status'] === 'completed') ? 'selected' : ''; ?>>Completed</option>
                                <option value="ongoing" <?php echo ($edit_project && $edit_project['status'] === 'ongoing') ? 'selected' : ''; ?>>Ongoing</option>
                                <option value="planned" <?php echo ($edit_project && $edit_project['status'] === 'planned') ? 'selected' : ''; ?>>Planned</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="client">Client</label>
                            <input type="text" id="client" name="client" 
                                   value="<?php echo $edit_project ? htmlspecialchars($edit_project['client']) : ''; ?>"
                                   placeholder="Client name or organization">
                        </div>
                        
                        <div class="form-group">
                            <label for="location">Location</label>
                            <input type="text" id="location" name="location" 
                                   value="<?php echo $edit_project ? htmlspecialchars($edit_project['location']) : ''; ?>"
                                   placeholder="City, State/Country">
                        </div>
                        
                        <div class="form-group">
                            <label for="year">Year</label>
                            <input type="number" id="year" name="year" min="1900" max="2050"
                                   value="<?php echo $edit_project ? $edit_project['year'] : date('Y'); ?>">
                        </div>
                        
                        <div class="form-group">
                            <!-- Empty space for layout -->
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="description">Project Description</label>
                            <textarea id="description" name="description" rows="3" 
                                      placeholder="Brief summary of the project for listings"><?php echo $edit_project ? htmlspecialchars($edit_project['description']) : ''; ?></textarea>
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="featured_image">Featured Image</label>
                            <?php if ($edit_project && $edit_project['featured_image']): ?>
                                <div style="margin-bottom: 1rem;">
                                    <img src="<?php echo $edit_project['featured_image']; ?>" alt="Current Featured Image" class="image-preview">
                                    <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Current featured image</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" id="featured_image" name="featured_image" accept="image/*">
                            <small style="color: #666; display: block; margin-top: 0.25rem;">Main project image. Recommended size: 1200x800px or larger.</small>
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="gallery_images">Project Gallery</label>
                            <?php if ($edit_project && $edit_project['gallery_images']): ?>
                                <div class="gallery-preview">
                                    <?php 
                                    $gallery_images = explode(',', $edit_project['gallery_images']);
                                    foreach ($gallery_images as $image): 
                                        if (!empty(trim($image))):
                                    ?>
                                        <img src="<?php echo trim($image); ?>" alt="Gallery Image">
                                    <?php 
                                        endif;
                                    endforeach; 
                                    ?>
                                </div>
                                <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Current gallery images</p>
                            <?php endif; ?>
                            <input type="file" id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                            <small style="color: #666; display: block; margin-top: 0.25rem;">
                                Select multiple images for the project gallery. Hold Ctrl/Cmd to select multiple files.
                            </small>
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="content">Project Details</label>
                            <textarea id="content" name="content" class="content-editor" 
                                      placeholder="Detailed project information, challenges, solutions, etc. HTML formatting supported."><?php echo $edit_project ? htmlspecialchars($edit_project['content']) : ''; ?></textarea>
                            <small style="color: #666; display: block; margin-top: 0.25rem;">
                                Full project details with HTML formatting. Use &lt;h2&gt;, &lt;h3&gt;, &lt;p&gt;, &lt;ul&gt;, &lt;ol&gt;, etc.
                            </small>
                        </div>
                    </div>
                    
                    <div style="margin-top: 2rem;">
                        <button type="submit" class="btn"><?php echo $edit_project ? 'Update Project' : 'Add Project'; ?></button>
                        <button type="button" class="btn btn-secondary" onclick="cancelForm()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Projects List -->
        <div class="admin-card">
            <div class="admin-card-header">
                <span>All Projects (<?php echo count($projects); ?>)</span>
            </div>
            
            <div class="admin-card-body">
                <?php if (empty($projects)): ?>
                    <p style="text-align: center; color: #666; padding: 2rem;">No projects found. <a href="#" onclick="toggleForm()">Add your first project</a>.</p>
                <?php else: ?>
                    <table class="projects-table">
                        <thead>
                            <tr>
                                <th>Project</th>
                                <th>Category</th>
                                <th>Client</th>
                                <th>Location</th>
                                <th>Year</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($projects as $project): ?>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 1rem;">
                                        <?php if ($project['featured_image']): ?>
                                            <img src="<?php echo $project['featured_image']; ?>" alt="<?php echo htmlspecialchars($project['title']); ?>" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;">
                                        <?php endif; ?>
                                        <div>
                                            <strong><?php echo htmlspecialchars($project['title']); ?></strong>
                                            <br><small><a href="<?php echo siteUrl('projects/' . $project['slug']); ?>" target="_blank" style="color: #E67E22;">View Project</a></small>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($project['category']); ?></td>
                                <td><?php echo htmlspecialchars($project['client'] ?: 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($project['location'] ?: 'N/A'); ?></td>
                                <td><?php echo $project['year']; ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $project['status']; ?>">
                                        <?php echo ucfirst($project['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?edit=<?php echo $project['id']; ?>" class="btn btn-secondary btn-small">Edit</a>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this project?');">
                                            <input type="hidden" name="action" value="delete_project">
                                            <input type="hidden" name="id" value="<?php echo $project['id']; ?>">
                                            <button type="submit" class="btn btn-danger btn-small">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script>
        function toggleForm() {
            const form = document.getElementById('projectForm');
            form.classList.toggle('active');
            
            if (form.classList.contains('active')) {
                document.getElementById('title').focus();
            }
        }
        
        function cancelForm() {
            window.location.href = 'projects.php';
        }
        
        function generateSlug() {
            const title = document.getElementById('title').value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('slug').value = slug;
        }
        
        // Auto-resize content textarea
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('content');
            if (textarea) {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            }
            
            // Show file count for gallery images
            const galleryInput = document.getElementById('gallery_images');
            if (galleryInput) {
                galleryInput.addEventListener('change', function() {
                    const fileCount = this.files.length;
                    let info = document.querySelector('.gallery-file-info');
                    if (!info) {
                        info = document.createElement('div');
                        info.className = 'file-info gallery-file-info';
                        this.parentNode.appendChild(info);
                    }
                    info.textContent = fileCount > 0 ? `${fileCount} file(s) selected` : '';
                });
            }
        });
    </script>
</body>
</html>
