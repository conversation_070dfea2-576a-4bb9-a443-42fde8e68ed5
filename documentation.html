<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monolith Design Co. - Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1A1A1A;
            border-bottom: 3px solid #E67E22;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #E67E22;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        h3 {
            color: #1A1A1A;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .highlight {
            background: #FDF6F0;
            padding: 15px;
            border-left: 4px solid #E67E22;
            margin: 20px 0;
        }
        .warning {
            background: #FFF3CD;
            padding: 15px;
            border-left: 4px solid #FFC107;
            margin: 20px 0;
        }
        .success {
            background: #D4EDDA;
            padding: 15px;
            border-left: 4px solid #28A745;
            margin: 20px 0;
        }
        code {
            background: #F8F9FA;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #F8F9FA;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #E9ECEF;
        }
        .file-tree {
            background: #F8F9FA;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            border: 1px solid #E9ECEF;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-item {
            background: #F8F9FA;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #E9ECEF;
        }
        .toc {
            background: #F8F9FA;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style: none;
            padding-left: 20px;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            color: #E67E22;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ Monolith Design Co. - Professional Engineering & Architecture Website</h1>
        
        <div class="highlight">
            <strong>Version:</strong> 1.0.0<br>
            <strong>Release Date:</strong> January 2025<br>
            <strong>Compatibility:</strong> PHP 7.4+, MySQL 5.7+<br>
            <strong>License:</strong> Regular License / Extended License Available
        </div>

        <div class="toc">
            <h3>📑 Table of Contents</h3>
            <ul>
                <li><a href="#overview">1. Overview & Features</a></li>
                <li><a href="#requirements">2. Server Requirements</a></li>
                <li><a href="#installation">3. Installation Guide</a></li>
                <li><a href="#configuration">4. Configuration</a></li>
                <li><a href="#admin-panel">5. Admin Panel Guide</a></li>
                <li><a href="#customization">6. Customization</a></li>
                <li><a href="#troubleshooting">7. Troubleshooting</a></li>
                <li><a href="#support">8. Support & Updates</a></li>
            </ul>
        </div>

        <h2 id="overview">1. 🎯 Overview & Features</h2>
        
        <p>Monolith Design Co. is a comprehensive, professional website theme specifically designed for engineering and architectural firms. Built with PHP and featuring a powerful admin dashboard, this theme combines elegant design with robust functionality.</p>

        <div class="feature-grid">
            <div class="feature-item">
                <h4>🎨 Design System</h4>
                <ul>
                    <li>8-point grid system</li>
                    <li>Montserrat & Lato typography</li>
                    <li>Customizable accent colors</li>
                    <li>Mobile-first responsive design</li>
                </ul>
            </div>
            <div class="feature-item">
                <h4>📱 Responsive Design</h4>
                <ul>
                    <li>Desktop (1200px+)</li>
                    <li>Tablet (768px-1199px)</li>
                    <li>Mobile (480px-767px)</li>
                    <li>Small Mobile (<480px)</li>
                </ul>
            </div>
            <div class="feature-item">
                <h4>🔧 Technical Features</h4>
                <ul>
                    <li>Clean URLs (no .php extensions)</li>
                    <li>SEO optimized</li>
                    <li>Secure coding practices</li>
                    <li>File upload system</li>
                </ul>
            </div>
            <div class="feature-item">
                <h4>📊 Content Management</h4>
                <ul>
                    <li>Hero slider management</li>
                    <li>Service pages</li>
                    <li>Project portfolio</li>
                    <li>Team profiles</li>
                </ul>
            </div>
        </div>

        <h2 id="requirements">2. 🖥️ Server Requirements</h2>
        
        <div class="highlight">
            <strong>Minimum Requirements:</strong>
            <ul>
                <li><strong>PHP:</strong> 7.4 or higher (8.0+ recommended)</li>
                <li><strong>MySQL:</strong> 5.7 or higher (8.0+ recommended)</li>
                <li><strong>Web Server:</strong> Apache with mod_rewrite enabled</li>
                <li><strong>Disk Space:</strong> 50MB minimum</li>
                <li><strong>Memory:</strong> 128MB minimum (256MB recommended)</li>
            </ul>
        </div>

        <h3>Required PHP Extensions</h3>
        <ul>
            <li>PDO & PDO_MySQL</li>
            <li>GD or ImageMagick (for image processing)</li>
            <li>JSON</li>
            <li>Session</li>
        </ul>

        <h2 id="installation">3. 🚀 Installation Guide</h2>

        <h3>Step 1: Upload Files</h3>
        <div class="highlight">
            <ol>
                <li>Extract the theme files from the downloaded zip</li>
                <li>Upload all files to your web server directory</li>
                <li>Ensure proper file permissions (755 for directories, 644 for files)</li>
            </ol>
        </div>

        <h3>Step 2: Database Setup (Automatic)</h3>
        <div class="success">
            <strong>Recommended Method:</strong>
            <ol>
                <li>Navigate to <code>yourwebsite.com/install.php</code></li>
                <li>Enter your database credentials</li>
                <li>Click "Install Database"</li>
                <li>Delete <code>install.php</code> after successful installation</li>
            </ol>
        </div>

        <h3>Step 3: Database Setup (Manual)</h3>
        <div class="warning">
            <strong>Alternative Method:</strong>
            <ol>
                <li>Create a MySQL database named <code>monolith_design</code></li>
                <li>Import the <code>database.sql</code> file</li>
                <li>Update database credentials in <code>config.php</code></li>
            </ol>
        </div>

        <h3>Step 4: Configuration</h3>
        <p>Edit <code>config.php</code> with your specific settings:</p>
        <pre><code>// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// Site Configuration
define('SITE_NAME', 'Your Company Name');
define('SITE_TAGLINE', 'Your Tagline');
define('SITE_URL', 'https://yourwebsite.com');</code></pre>

        <h2 id="configuration">4. ⚙️ Configuration</h2>

        <h3>Basic Settings</h3>
        <div class="file-tree">config.php - Main configuration file
├── Database settings
├── Site information
├── Upload settings
├── Security settings
└── Error reporting (set to 0 for production)</div>

        <h3>URL Structure</h3>
        <p>The theme uses clean URLs enabled by <code>.htaccess</code>:</p>
        <ul>
            <li><code>/</code> - Homepage</li>
            <li><code>/about</code> - About page</li>
            <li><code>/services</code> - Services overview</li>
            <li><code>/service/service-name</code> - Individual service</li>
            <li><code>/projects</code> - Projects portfolio</li>
            <li><code>/contact</code> - Contact page</li>
        </ul>

        <h2 id="admin-panel">5. 🎛️ Admin Panel Guide</h2>

        <h3>Accessing the Admin Panel</h3>
        <div class="highlight">
            <strong>URL:</strong> <code>yourwebsite.com/admin/</code><br>
            <strong>Default Login:</strong> admin / admin123<br>
            <strong>⚠️ Important:</strong> Change the default password immediately!
        </div>

        <h3>Admin Sections</h3>
        
        <h4>Theme Options</h4>
        <ul>
            <li><strong>General Settings:</strong> Site name, tagline, contact info</li>
            <li><strong>Colors & Branding:</strong> Accent colors, logo upload</li>
            <li><strong>Social Media:</strong> Social platform links</li>
            <li><strong>Homepage:</strong> Hero section, CTA settings</li>
            <li><strong>Contact Page:</strong> Form settings, map embed</li>
        </ul>

        <h4>Content Management</h4>
        <ul>
            <li><strong>Sliders:</strong> Manage homepage hero slider</li>
            <li><strong>Services:</strong> Add/edit service pages</li>
            <li><strong>Projects:</strong> Manage portfolio items</li>
            <li><strong>Team:</strong> Staff member profiles</li>
            <li><strong>Testimonials:</strong> Client feedback</li>
            <li><strong>Blog:</strong> News and articles</li>
        </ul>

        <h4>Footer Management</h4>
        <ul>
            <li>Footer logo and tagline</li>
            <li>Navigation sections (Company, Services, Projects)</li>
            <li>Contact information</li>
            <li>Social media links</li>
            <li>Newsletter subscription</li>
        </ul>

        <h2 id="customization">6. 🎨 Customization</h2>

        <h3>Color Scheme</h3>
        <p>Change colors through the admin panel or by editing CSS variables:</p>
        <pre><code>:root {
    --primary-color: #1A1A1A;    /* Dark elements */
    --secondary-color: #F5F5F5;  /* Light backgrounds */
    --accent-color: #E67E22;     /* Buttons, links */
}</code></pre>

        <h3>Typography</h3>
        <p>The theme uses Google Fonts (Montserrat & Lato). To change fonts, edit the CSS imports and font-family declarations.</p>

        <h3>Adding Custom Pages</h3>
        <div class="highlight">
            <ol>
                <li>Create a new PHP file in the root directory</li>
                <li>Include the header: <code>&lt;?php loadTemplate('header'); ?&gt;</code></li>
                <li>Add your content</li>
                <li>Include the footer: <code>&lt;?php loadFooter(); ?&gt;</code></li>
            </ol>
        </div>

        <h3>Image Management</h3>
        <p>Replace placeholder images in <code>/assets/images/</code>:</p>
        <ul>
            <li><strong>Logo:</strong> SVG format recommended</li>
            <li><strong>Hero backgrounds:</strong> High-quality architectural images (1920x1080)</li>
            <li><strong>Service icons:</strong> SVG icons (64x64)</li>
            <li><strong>Project photos:</strong> Portfolio images (800x600)</li>
        </ul>

        <h2 id="troubleshooting">7. 🔧 Troubleshooting</h2>

        <h3>Common Issues</h3>
        
        <div class="warning">
            <strong>Issue:</strong> Clean URLs not working<br>
            <strong>Solution:</strong> Ensure Apache mod_rewrite is enabled and .htaccess is properly uploaded
        </div>

        <div class="warning">
            <strong>Issue:</strong> Database connection error<br>
            <strong>Solution:</strong> Verify database credentials in config.php
        </div>

        <div class="warning">
            <strong>Issue:</strong> Images not uploading<br>
            <strong>Solution:</strong> Check file permissions on /assets/images/uploads/ (should be 755)
        </div>

        <div class="warning">
            <strong>Issue:</strong> Admin panel not accessible<br>
            <strong>Solution:</strong> Clear browser cache and check .htaccess rules
        </div>

        <h3>File Permissions</h3>
        <ul>
            <li><strong>Directories:</strong> 755</li>
            <li><strong>PHP Files:</strong> 644</li>
            <li><strong>Upload Directory:</strong> 755 (writable)</li>
            <li><strong>Config files:</strong> 644 (or 600 for extra security)</li>
        </ul>

        <h3>Performance Optimization</h3>
        <ul>
            <li>Enable gzip compression on your server</li>
            <li>Use a CDN for static assets</li>
            <li>Enable browser caching</li>
            <li>Optimize images before upload</li>
        </ul>

        <h2 id="support">8. 📞 Support & Updates</h2>

        <h3>Getting Support</h3>
        <div class="success">
            <p>For support with this theme:</p>
            <ol>
                <li>Check this documentation first</li>
                <li>Review the code comments</li>
                <li>Test on a development environment</li>
                <li>Contact support through ThemeForest if issues persist</li>
            </ol>
        </div>

        <h3>Version History</h3>
        <ul>
            <li><strong>v1.0.0</strong> - Initial release with full functionality</li>
        </ul>

        <h3>Security Best Practices</h3>
        <ul>
            <li>Change default admin password immediately</li>
            <li>Keep PHP and MySQL updated</li>
            <li>Regular backups of database and files</li>
            <li>Use strong passwords</li>
            <li>Monitor for suspicious activity</li>
        </ul>

        <hr style="margin: 40px 0; border: none; height: 1px; background: #E9ECEF;">

        <div class="highlight">
            <h3>🏗️ Thank You for Choosing Monolith Design Co.!</h3>
            <p>This theme was crafted with precision and attention to detail. We hope it serves your engineering and architectural firm well. If you're happy with the theme, please consider leaving a positive review on ThemeForest!</p>
            
            <p><strong>Built with precision, designed for excellence.</strong></p>
        </div>
    </div>
</body>
</html>
