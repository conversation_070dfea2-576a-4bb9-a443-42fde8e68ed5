<?php
/**
 * Monolith Design Co. - Admin Dashboard
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Simple authentication - session already started in functions.php
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// Handle form submissions
$message = '';
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_general':
                updateThemeOption('site_name', sanitizeInput($_POST['site_name']));
                updateThemeOption('site_tagline', sanitizeInput($_POST['site_tagline']));
                updateThemeOption('phone_number', sanitizeInput($_POST['phone_number']));
                updateThemeOption('email', sanitizeInput($_POST['email']));
                updateThemeOption('address', sanitizeInput($_POST['address']));
                $message = 'General settings updated successfully!';
                break;
                
            case 'update_colors':
                updateThemeOption('accent_color', sanitizeInput($_POST['accent_color']));
                updateThemeOption('primary_color', sanitizeInput($_POST['primary_color']));
                updateThemeOption('secondary_color', sanitizeInput($_POST['secondary_color']));
                $message = 'Color settings updated successfully!';
                break;
                
            case 'update_social':
                updateThemeOption('facebook_url', sanitizeInput($_POST['facebook_url']));
                updateThemeOption('twitter_url', sanitizeInput($_POST['twitter_url']));
                updateThemeOption('linkedin_url', sanitizeInput($_POST['linkedin_url']));
                updateThemeOption('instagram_url', sanitizeInput($_POST['instagram_url']));
                $message = 'Social media links updated successfully!';
                break;
                
            case 'update_footer':
                updateThemeOption('footer_about_text', sanitizeInput($_POST['footer_about_text']));
                updateThemeOption('footer_copyright', sanitizeInput($_POST['footer_copyright']));
                $message = 'Footer content updated successfully!';
                break;
                
            case 'upload_logo':
                if (isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
                    $upload = uploadFile($_FILES['logo_file'], ['jpg', 'jpeg', 'png', 'svg']);
                    if ($upload['success']) {
                        updateThemeOption('site_logo', $upload['url']);
                        $message = 'Logo uploaded successfully!';
                    } else {
                        $message = 'Error uploading logo: ' . $upload['message'];
                    }
                }
                break;
                
            case 'upload_favicon':
                if (isset($_FILES['favicon_file']) && $_FILES['favicon_file']['error'] === UPLOAD_ERR_OK) {
                    $upload = uploadFile($_FILES['favicon_file'], ['ico', 'png']);
                    if ($upload['success']) {
                        updateThemeOption('favicon', $upload['url']);
                        $message = 'Favicon uploaded successfully!';
                    } else {
                        $message = 'Error uploading favicon: ' . $upload['message'];
                    }
                }
                break;

            case 'update_hero_cta':
                updateThemeOption('hero_cta_caption', sanitizeInput($_POST['hero_cta_caption']));
                updateThemeOption('hero_cta_title', sanitizeInput($_POST['hero_cta_title']));
                updateThemeOption('hero_cta_description', sanitizeInput($_POST['hero_cta_description']));
                updateThemeOption('hero_cta_button_text', sanitizeInput($_POST['hero_cta_button_text']));
                updateThemeOption('hero_cta_button_link', sanitizeInput($_POST['hero_cta_button_link']));

                // Handle background image upload
                if (isset($_FILES['hero_cta_background']) && $_FILES['hero_cta_background']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = '../assets/images/uploads/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }

                    $fileName = 'hero-cta-bg-' . time() . '.' . pathinfo($_FILES['hero_cta_background']['name'], PATHINFO_EXTENSION);
                    $uploadPath = $uploadDir . $fileName;

                    if (move_uploaded_file($_FILES['hero_cta_background']['tmp_name'], $uploadPath)) {
                        updateThemeOption('hero_cta_background', 'assets/images/uploads/' . $fileName);
                    }
                }

                $message = 'Hero CTA section updated successfully!';
                break;

            case 'update_contact':
                updateThemeOption('contact_hero_title', sanitizeInput($_POST['contact_hero_title']));
                updateThemeOption('contact_hero_description', sanitizeInput($_POST['contact_hero_description']));
                updateThemeOption('contact_info_title', sanitizeInput($_POST['contact_info_title']));
                updateThemeOption('contact_info_description', sanitizeInput($_POST['contact_info_description']));
                updateThemeOption('contact_form_title', sanitizeInput($_POST['contact_form_title']));
                updateThemeOption('contact_form_description', sanitizeInput($_POST['contact_form_description']));
                updateThemeOption('contact_map_title', sanitizeInput($_POST['contact_map_title']));
                updateThemeOption('contact_map_embed', $_POST['contact_map_embed']); // Don't sanitize HTML
                updateThemeOption('contact_show_map', sanitizeInput($_POST['contact_show_map']));
                updateThemeOption('business_hours', sanitizeInput($_POST['business_hours']));

                // Handle background image upload
                if (isset($_FILES['contact_hero_background']) && $_FILES['contact_hero_background']['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = '../assets/images/uploads/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }

                    $fileName = 'contact-hero-bg-' . time() . '.' . pathinfo($_FILES['contact_hero_background']['name'], PATHINFO_EXTENSION);
                    $uploadPath = $uploadDir . $fileName;

                    if (move_uploaded_file($_FILES['contact_hero_background']['tmp_name'], $uploadPath)) {
                        updateThemeOption('contact_hero_background', 'assets/images/uploads/' . $fileName);
                    }
                }

                $message = 'Contact page settings updated successfully!';
                break;
        }
    }
}

// Get current settings
$settings = [
    'site_name' => getThemeOption('site_name', SITE_NAME),
    'site_tagline' => getThemeOption('site_tagline', SITE_TAGLINE),
    'phone_number' => getThemeOption('phone_number', '+****************'),
    'email' => getThemeOption('email', '<EMAIL>'),
    'address' => getThemeOption('address', '123 Design Street\nArchitecture City, AC 12345'),
    'accent_color' => getThemeOption('accent_color', '#E67E22'),
    'primary_color' => getThemeOption('primary_color', '#1A1A1A'),
    'secondary_color' => getThemeOption('secondary_color', '#F5F5F5'),
    'facebook_url' => getThemeOption('facebook_url', ''),
    'twitter_url' => getThemeOption('twitter_url', ''),
    'linkedin_url' => getThemeOption('linkedin_url', ''),
    'instagram_url' => getThemeOption('instagram_url', ''),
    'footer_about_text' => getThemeOption('footer_about_text', 'We are a leading engineering and architectural firm dedicated to creating innovative structures with precision and excellence.'),
    'footer_copyright' => getThemeOption('footer_copyright', 'All rights reserved.'),
    'site_logo' => getThemeOption('site_logo', themeUrl('images/logo.svg')),
    'favicon' => getThemeOption('favicon', themeUrl('images/favicon.ico')),
    'hero_cta_caption' => getThemeOption('hero_cta_caption', "Let's Connect"),
    'hero_cta_title' => getThemeOption('hero_cta_title', 'Start Your Project Today'),
    'hero_cta_description' => getThemeOption('hero_cta_description', "Our strategic and technical advisory service isn't based on a hunch. It's backed by years of experience, extensive technical knowledge and data-driven insights. With our trusted advice,"),
    'hero_cta_button_text' => getThemeOption('hero_cta_button_text', 'GET IN TOUCH'),
    'hero_cta_button_link' => getThemeOption('hero_cta_button_link', 'contact'),
    'hero_cta_background' => getThemeOption('hero_cta_background', ''),
    'contact_hero_title' => getThemeOption('contact_hero_title', 'Get In Touch'),
    'contact_hero_description' => getThemeOption('contact_hero_description', 'Ready to start your project? We\'re here to help bring your vision to life with our expertise in engineering and architectural design.'),
    'contact_hero_background' => getThemeOption('contact_hero_background', ''),
    'contact_info_title' => getThemeOption('contact_info_title', 'Contact Information'),
    'contact_info_description' => getThemeOption('contact_info_description', 'Get in touch with us today to discuss your project requirements.'),
    'contact_form_title' => getThemeOption('contact_form_title', 'Send us a Message'),
    'contact_form_description' => getThemeOption('contact_form_description', 'Fill out the form below and we\'ll get back to you as soon as possible.'),
    'contact_map_title' => getThemeOption('contact_map_title', 'Find Us'),
    'contact_map_embed' => getThemeOption('contact_map_embed', ''),
    'contact_show_map' => getThemeOption('contact_show_map', '1'),
    'business_hours' => getThemeOption('business_hours', 'Monday - Friday: 8:00 AM - 6:00 PM\nSaturday: 9:00 AM - 4:00 PM\nSunday: Closed')
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo $settings['site_name']; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .admin-header {
            background: #1A1A1A;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav {
            background: #2c3e50;
            padding: 1rem 2rem;
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            margin-right: 2rem;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #E67E22;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-card-header {
            background: #34495e;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
        }
        
        .admin-card-body {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }
        
        input[type="text"],
        input[type="email"],
        input[type="color"],
        textarea,
        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="color"]:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #E67E22;
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            background: #E67E22;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #d35400;
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .message {
            background: #fdf6f0;
            color: #E67E22;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .color-preview {
            width: 50px;
            height: 50px;
            border-radius: 4px;
            border: 2px solid #ddd;
            display: inline-block;
            margin-left: 1rem;
            vertical-align: middle;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 100px;
            margin-top: 0.5rem;
            border: 2px dashed #ddd;
            padding: 0.5rem;
            border-radius: 4px;
        }
        
        .file-upload {
            position: relative;
            display: inline-block;
        }
        
        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .tabs {
            display: flex;
            background: #ecf0f1;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 1rem;
            background: #bdc3c7;
            color: #2c3e50;
            cursor: pointer;
            text-align: center;
            font-weight: 600;
            transition: background 0.3s;
        }
        
        .tab.active {
            background: #34495e;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Monolith Design Co. - Admin Dashboard</h1>
        <div>
            <a href="<?php echo siteUrl(); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Website</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php" class="active">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="services.php">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <div class="admin-card">
            <div class="tabs">
                <div class="tab active" onclick="showTab('general')">General Settings</div>
                <div class="tab" onclick="showTab('colors')">Colors & Branding</div>
                <div class="tab" onclick="showTab('social')">Social Media</div>
                <div class="tab" onclick="showTab('footer')">Footer Content</div>
                <div class="tab" onclick="showTab('hero_cta')">Hero CTA Section</div>
                <div class="tab" onclick="showTab('contact')">Contact Page</div>
            </div>
            
            <!-- General Settings Tab -->
            <div id="general" class="tab-content active">
                <div class="admin-card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_general">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="site_name">Site Name</label>
                                <input type="text" id="site_name" name="site_name" value="<?php echo htmlspecialchars($settings['site_name']); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="site_tagline">Site Tagline</label>
                                <input type="text" id="site_tagline" name="site_tagline" value="<?php echo htmlspecialchars($settings['site_tagline']); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="phone_number">Phone Number</label>
                                <input type="text" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($settings['phone_number']); ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($settings['email']); ?>">
                            </div>
                            
                            <div class="form-group full-width">
                                <label for="address">Address</label>
                                <textarea id="address" name="address"><?php echo htmlspecialchars($settings['address']); ?></textarea>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn">Update General Settings</button>
                    </form>
                </div>
            </div>
            
            <!-- Colors & Branding Tab -->
            <div id="colors" class="tab-content">
                <div class="admin-card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_colors">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="accent_color">Accent Color (Buttons, Links)</label>
                                <input type="color" id="accent_color" name="accent_color" value="<?php echo $settings['accent_color']; ?>">
                                <div class="color-preview" style="background-color: <?php echo $settings['accent_color']; ?>"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="primary_color">Primary Color (Dark Elements)</label>
                                <input type="color" id="primary_color" name="primary_color" value="<?php echo $settings['primary_color']; ?>">
                                <div class="color-preview" style="background-color: <?php echo $settings['primary_color']; ?>"></div>
                            </div>
                            
                            <div class="form-group full-width">
                                <label for="secondary_color">Secondary Color (Light Backgrounds)</label>
                                <input type="color" id="secondary_color" name="secondary_color" value="<?php echo $settings['secondary_color']; ?>">
                                <div class="color-preview" style="background-color: <?php echo $settings['secondary_color']; ?>"></div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn">Update Colors</button>
                    </form>
                    
                    <hr style="margin: 2rem 0;">
                    
                    <h3>Logo & Favicon</h3>
                    
                    <div class="form-grid" style="margin-top: 1rem;">
                        <div class="form-group">
                            <label>Current Logo</label>
                            <img src="<?php echo $settings['site_logo']; ?>" alt="Logo" class="image-preview">
                            <form method="POST" enctype="multipart/form-data" style="margin-top: 1rem;">
                                <input type="hidden" name="action" value="upload_logo">
                                <div class="file-upload">
                                    <button type="button" class="btn btn-secondary">Choose Logo File</button>
                                    <input type="file" name="logo_file" accept=".jpg,.jpeg,.png,.svg" onchange="this.form.submit()">
                                </div>
                            </form>
                        </div>
                        
                        <div class="form-group">
                            <label>Current Favicon</label>
                            <img src="<?php echo $settings['favicon']; ?>" alt="Favicon" class="image-preview">
                            <form method="POST" enctype="multipart/form-data" style="margin-top: 1rem;">
                                <input type="hidden" name="action" value="upload_favicon">
                                <div class="file-upload">
                                    <button type="button" class="btn btn-secondary">Choose Favicon File</button>
                                    <input type="file" name="favicon_file" accept=".ico,.png" onchange="this.form.submit()">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Social Media Tab -->
            <div id="social" class="tab-content">
                <div class="admin-card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_social">
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="facebook_url">Facebook URL</label>
                                <input type="text" id="facebook_url" name="facebook_url" value="<?php echo htmlspecialchars($settings['facebook_url']); ?>" placeholder="https://facebook.com/yourpage">
                            </div>
                            
                            <div class="form-group">
                                <label for="twitter_url">Twitter URL</label>
                                <input type="text" id="twitter_url" name="twitter_url" value="<?php echo htmlspecialchars($settings['twitter_url']); ?>" placeholder="https://twitter.com/yourhandle">
                            </div>
                            
                            <div class="form-group">
                                <label for="linkedin_url">LinkedIn URL</label>
                                <input type="text" id="linkedin_url" name="linkedin_url" value="<?php echo htmlspecialchars($settings['linkedin_url']); ?>" placeholder="https://linkedin.com/company/yourcompany">
                            </div>
                            
                            <div class="form-group">
                                <label for="instagram_url">Instagram URL</label>
                                <input type="text" id="instagram_url" name="instagram_url" value="<?php echo htmlspecialchars($settings['instagram_url']); ?>" placeholder="https://instagram.com/yourhandle">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn">Update Social Media Links</button>
                    </form>
                </div>
            </div>
            
            <!-- Footer Content Tab -->
            <div id="footer" class="tab-content">
                <div class="admin-card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_footer">
                        
                        <div class="form-group">
                            <label for="footer_about_text">Footer About Text</label>
                            <textarea id="footer_about_text" name="footer_about_text" rows="4"><?php echo htmlspecialchars($settings['footer_about_text']); ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="footer_copyright">Footer Copyright Text</label>
                            <input type="text" id="footer_copyright" name="footer_copyright" value="<?php echo htmlspecialchars($settings['footer_copyright']); ?>">
                            <small style="color: #666; display: block; margin-top: 0.5rem;">The year and site name will be added automatically</small>
                        </div>
                        
                        <button type="submit" class="btn">Update Footer Content</button>
                    </form>
                </div>
            </div>

            <!-- Hero CTA Section Tab -->
            <div id="hero_cta" class="tab-content">
                <div class="admin-card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_hero_cta">

                        <div class="form-group">
                            <label for="hero_cta_caption">Caption Text</label>
                            <input type="text" id="hero_cta_caption" name="hero_cta_caption" value="<?php echo htmlspecialchars($settings['hero_cta_caption']); ?>">
                            <small style="color: #666; display: block; margin-top: 0.5rem;">Small text above the main title</small>
                        </div>

                        <div class="form-group">
                            <label for="hero_cta_title">Main Title</label>
                            <input type="text" id="hero_cta_title" name="hero_cta_title" value="<?php echo htmlspecialchars($settings['hero_cta_title']); ?>">
                        </div>

                        <div class="form-group">
                            <label for="hero_cta_description">Description Text</label>
                            <textarea id="hero_cta_description" name="hero_cta_description" rows="4"><?php echo htmlspecialchars($settings['hero_cta_description']); ?></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="hero_cta_button_text">Button Text</label>
                                <input type="text" id="hero_cta_button_text" name="hero_cta_button_text" value="<?php echo htmlspecialchars($settings['hero_cta_button_text']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="hero_cta_button_link">Button Link</label>
                                <input type="text" id="hero_cta_button_link" name="hero_cta_button_link" value="<?php echo htmlspecialchars($settings['hero_cta_button_link']); ?>">
                                <small style="color: #666; display: block; margin-top: 0.5rem;">Page slug (e.g., 'contact', 'about') or full URL</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="hero_cta_background">Background Image</label>
                            <?php if (!empty($settings['hero_cta_background'])): ?>
                                <div style="margin-bottom: 1rem;">
                                    <img src="<?php echo siteUrl($settings['hero_cta_background']); ?>" alt="Current Background" style="max-width: 200px; height: auto; border-radius: 4px;">
                                    <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Current background image</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" id="hero_cta_background" name="hero_cta_background" accept="image/*">
                            <small style="color: #666; display: block; margin-top: 0.5rem;">Upload a new background image (JPG, PNG, WebP)</small>
                        </div>

                        <button type="submit" class="btn">Update Hero CTA Section</button>
                    </form>
                </div>
            </div>

            <!-- Contact Page Tab -->
            <div id="contact" class="tab-content">
                <div class="admin-card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_contact">

                        <h3 style="margin-bottom: 1.5rem; color: var(--primary-color);">Hero Section</h3>
                        
                        <div class="form-group">
                            <label for="contact_hero_title">Hero Title</label>
                            <input type="text" id="contact_hero_title" name="contact_hero_title" value="<?php echo htmlspecialchars($settings['contact_hero_title']); ?>">
                        </div>

                        <div class="form-group">
                            <label for="contact_hero_description">Hero Description</label>
                            <textarea id="contact_hero_description" name="contact_hero_description" rows="3"><?php echo htmlspecialchars($settings['contact_hero_description']); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="contact_hero_background">Hero Background Image</label>
                            <?php if (!empty($settings['contact_hero_background'])): ?>
                                <div style="margin-bottom: 1rem;">
                                    <img src="<?php echo siteUrl($settings['contact_hero_background']); ?>" alt="Current Background" style="max-width: 200px; height: auto; border-radius: 4px;">
                                    <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Current background image</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" id="contact_hero_background" name="contact_hero_background" accept="image/*">
                            <small style="color: #666; display: block; margin-top: 0.5rem;">Upload a background image for the contact page hero section</small>
                        </div>

                        <hr style="margin: 2rem 0; border: none; border-top: 1px solid #e1e5e9;">
                        <h3 style="margin-bottom: 1.5rem; color: var(--primary-color);">Contact Information Section</h3>
                        
                        <div class="form-group">
                            <label for="contact_info_title">Contact Info Title</label>
                            <input type="text" id="contact_info_title" name="contact_info_title" value="<?php echo htmlspecialchars($settings['contact_info_title']); ?>">
                        </div>

                        <div class="form-group">
                            <label for="contact_info_description">Contact Info Description</label>
                            <textarea id="contact_info_description" name="contact_info_description" rows="2"><?php echo htmlspecialchars($settings['contact_info_description']); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="business_hours">Business Hours</label>
                            <textarea id="business_hours" name="business_hours" rows="3"><?php echo htmlspecialchars($settings['business_hours']); ?></textarea>
                            <small style="color: #666; display: block; margin-top: 0.5rem;">Use line breaks for multiple lines</small>
                        </div>

                        <hr style="margin: 2rem 0; border: none; border-top: 1px solid #e1e5e9;">
                        <h3 style="margin-bottom: 1.5rem; color: var(--primary-color);">Contact Form Section</h3>
                        
                        <div class="form-group">
                            <label for="contact_form_title">Contact Form Title</label>
                            <input type="text" id="contact_form_title" name="contact_form_title" value="<?php echo htmlspecialchars($settings['contact_form_title']); ?>">
                        </div>

                        <div class="form-group">
                            <label for="contact_form_description">Contact Form Description</label>
                            <textarea id="contact_form_description" name="contact_form_description" rows="2"><?php echo htmlspecialchars($settings['contact_form_description']); ?></textarea>
                        </div>

                        <hr style="margin: 2rem 0; border: none; border-top: 1px solid #e1e5e9;">
                        <h3 style="margin-bottom: 1.5rem; color: var(--primary-color);">Map Section</h3>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="contact_show_map" value="1" <?php echo ($settings['contact_show_map'] === '1') ? 'checked' : ''; ?> style="margin-right: 0.5rem;">
                                Show Map Section
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="contact_map_title">Map Section Title</label>
                            <input type="text" id="contact_map_title" name="contact_map_title" value="<?php echo htmlspecialchars($settings['contact_map_title']); ?>">
                        </div>

                        <div class="form-group">
                            <label for="contact_map_embed">Google Maps Embed Code</label>
                            <textarea id="contact_map_embed" name="contact_map_embed" rows="5" placeholder="<iframe src=&quot;https://www.google.com/maps/embed?pb=...&quot; width=&quot;600&quot; height=&quot;450&quot; style=&quot;border:0;&quot; allowfullscreen=&quot;&quot; loading=&quot;lazy&quot; referrerpolicy=&quot;no-referrer-when-downgrade&quot;></iframe>"><?php echo htmlspecialchars($settings['contact_map_embed']); ?></textarea>
                            <small style="color: #666; display: block; margin-top: 0.5rem;">
                                Paste the embed code from Google Maps. 
                                <a href="https://www.google.com/maps" target="_blank" style="color: var(--accent-color);">Get embed code →</a>
                            </small>
                        </div>

                        <button type="submit" class="btn">Update Contact Page Settings</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        // Update color previews when color inputs change
        document.addEventListener('DOMContentLoaded', function() {
            const colorInputs = document.querySelectorAll('input[type="color"]');
            colorInputs.forEach(input => {
                input.addEventListener('change', function() {
                    const preview = this.parentNode.querySelector('.color-preview');
                    if (preview) {
                        preview.style.backgroundColor = this.value;
                    }
                });
            });
        });
    </script>
</body>
</html>
