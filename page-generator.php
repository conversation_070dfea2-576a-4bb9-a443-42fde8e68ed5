<?php
/**
 * 🚀 Page Generator - Creates new pages with proper header/footer
 * Usage: Run this script and follow prompts
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';

if ($_POST && isset($_POST['create_page'])) {
    $pageName = sanitizeInput($_POST['page_name']);
    $pageTitle = sanitizeInput($_POST['page_title']);
    $pageDescription = sanitizeInput($_POST['page_description']);
    $includeHero = isset($_POST['include_hero']);
    
    $fileName = strtolower(str_replace(' ', '-', $pageName)) . '.php';
    
    if (file_exists($fileName)) {
        $message = "❌ File $fileName already exists!";
        $messageType = 'error';
    } else {
        // Generate page content
        $pageContent = generatePageTemplate($fileName, $pageTitle, $pageDescription, $includeHero);
        
        if (file_put_contents($fileName, $pageContent)) {
            $message = "✅ Page created successfully: $fileName";
            $messageType = 'success';
        } else {
            $message = "❌ Failed to create page: $fileName";
            $messageType = 'error';
        }
    }
}

function generatePageTemplate($fileName, $title, $description, $includeHero) {
    $heroSection = $includeHero ? '
        <!-- Page Hero -->
        <section class="page-hero">
            <div class="container">
                <h1>' . htmlspecialchars($title) . '</h1>
                <p>' . htmlspecialchars($description) . '</p>
            </div>
        </section>' : '';
    
    return '<?php
/**
 * ' . $title . ' Page
 * Generated: ' . date('Y-m-d H:i:s') . '
 */

define(\'MONOLITH_ACCESS\', true);
require_once \'config.php\';
require_once \'includes/functions.php\';

$pageTitle = \'' . addslashes($title) . '\';
$pageDescription = \'' . addslashes($description) . '\';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate(\'head\', [
        \'title\' => $pageTitle,
        \'description\' => $pageDescription
    ]); ?>
</head>
<body>
    <!-- 🎯 HEADER -->
    <?php loadTemplate(\'header\'); ?>

    <main class="page-content">' . $heroSection . '
        <!-- Main Content -->
        <section class="main-section">
            <div class="container">
                <h2>Welcome to ' . htmlspecialchars($title) . '</h2>
                <p>Add your content here...</p>
            </div>
        </section>
    </main>

    <!-- 🎯 FOOTER (NEW SYSTEM) -->
    <?php loadFooter(); ?>
</body>
</html>';
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>🚀 Page Generator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; max-width: 600px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        textarea { height: 80px; resize: vertical; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a87; }
        .message { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .checkbox-group { display: flex; align-items: center; gap: 10px; }
        .info { background: #d1ecf1; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Page Generator</h1>
        
        <div class="info">
            <strong>✨ This tool creates new pages with:</strong><br>
            ✅ Proper header inclusion<br>
            ✅ New footer system (loadFooter)<br>
            ✅ SEO-friendly structure<br>
            ✅ Responsive design<br>
        </div>

        <?php if (isset($message)): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="form-group">
                <label for="page_name">Page Name (used for filename):</label>
                <input type="text" id="page_name" name="page_name" placeholder="e.g., About Us, Contact, Blog" required>
                <small>Will create: page-name.php</small>
            </div>

            <div class="form-group">
                <label for="page_title">Page Title:</label>
                <input type="text" id="page_title" name="page_title" placeholder="e.g., About Our Company" required>
            </div>

            <div class="form-group">
                <label for="page_description">Page Description (for SEO):</label>
                <textarea id="page_description" name="page_description" placeholder="Brief description of the page content..." required></textarea>
            </div>

            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="include_hero" name="include_hero" checked>
                    <label for="include_hero">Include Hero Section</label>
                </div>
            </div>

            <button type="submit" name="create_page">🚀 Create Page</button>
        </form>

        <hr style="margin: 30px 0;">
        
        <h3>📋 Manual Instructions</h3>
        <p><strong>For any new page, always use:</strong></p>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
&lt;?php loadTemplate('header'); ?&gt;
&lt;!-- Your page content --&gt;
&lt;?php loadFooter(); ?&gt;
        </pre>
        
        <p><strong>❌ Don't use (old system):</strong></p>
        <pre style="background: #f8d7da; padding: 15px; border-radius: 4px;">
&lt;?php loadTemplate('footer'); ?&gt;  // ❌ OLD
        </pre>
    </div>
</body>
</html>
