{"version": "2.0.0", "tasks": [{"label": "Start MAMP Server for Testing", "type": "shell", "command": "echo 'Enhanced single-page templates created successfully!' && echo 'News detail template features:' && echo '- Breadcrumb navigation' && echo '- Hero section with category and meta info' && echo '- Social sharing buttons (Facebook, Twitter, LinkedIn, Email)' && echo '- Article tags and related articles' && echo '- Previous/Next article navigation' && echo '- Back to News button' && echo '- Responsive design' && echo '' && echo 'Project detail template features:' && echo '- Breadcrumb navigation' && echo '- Hero section with project status' && echo '- Project meta information sidebar' && echo '- Image gallery with lightbox' && echo '- Social sharing buttons' && echo '- Previous/Next project navigation' && echo '- View All Projects button' && echo '- Related projects section' && echo '- Responsive design' && echo '' && echo 'Both templates use the universal header system with consistent CSS loading.'", "group": "test", "isBackground": false}]}