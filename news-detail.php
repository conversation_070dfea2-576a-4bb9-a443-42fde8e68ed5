<?php
/**
 * News Detail Page
 * Displays individual blog post content
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get the slug from URL
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: ' . siteUrl('404'));
    exit;
}

// Fetch the blog post from database
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND status = 'published'");
$stmt->execute([$slug]);
$post = $stmt->fetch();

if (!$post) {
    header('Location: ' . siteUrl('404'));
    exit;
}

// Get related posts (same category, excluding current post)
$stmt = $db->prepare("SELECT * FROM blog_posts WHERE category = ? AND slug != ? AND status = 'published' ORDER BY published_at DESC LIMIT 3");
$stmt->execute([$post['category'], $slug]);
$related_posts = $stmt->fetchAll();

$pageTitle = htmlspecialchars($post['title']) . ' - Monolith Design';
$pageDescription = htmlspecialchars($post['meta_description'] ?: $post['excerpt']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($post['title']); ?>">
    <meta property="og:description" content="<?php echo $pageDescription; ?>">
    <meta property="og:image" content="<?php echo $post['featured_image']; ?>">
    <meta property="og:url" content="<?php echo siteUrl('news/' . $post['slug']); ?>">
    <meta property="og:type" content="article">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo themeUrl('css/arkify-style.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/work-section-clean.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/responsive.css'); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo themeUrl('images/favicon.ico'); ?>">
</head>

<body>
    <?php loadTemplate('header'); ?>

    <!-- Article Header -->
    <section class="article-header">
        <div class="container">
            <div class="article-breadcrumb">
                <a href="<?php echo siteUrl(); ?>">Home</a>
                <span>•</span>
                <a href="<?php echo siteUrl('news'); ?>">News</a>
                <span>•</span>
                <span><?php echo htmlspecialchars($post['title']); ?></span>
            </div>
            
            <div class="article-meta">
                <span class="article-category"><?php echo htmlspecialchars($post['category']); ?></span>
                <span>•</span>
                <span><?php echo date('F j, Y', strtotime($post['published_at'])); ?></span>
                <span>•</span>
                <span>By <?php echo htmlspecialchars($post['author']); ?></span>
            </div>
            
            <h1 class="article-title"><?php echo htmlspecialchars($post['title']); ?></h1>
            
            <?php if ($post['excerpt']): ?>
            <div class="article-excerpt">
                <p><?php echo htmlspecialchars($post['excerpt']); ?></p>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Featured Image -->
    <?php if ($post['featured_image']): ?>
    <section class="article-featured-image">
        <div class="container">
            <img src="<?php echo $post['featured_image']; ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" class="featured-image">
        </div>
    </section>
    <?php endif; ?>

    <!-- Article Content -->
    <section class="article-content">
        <div class="container">
            <div class="article-body">
                <?php echo $post['content']; ?>
            </div>
            
            <!-- Tags -->
            <?php if ($post['tags']): ?>
            <div class="article-tags">
                <h4>Tags:</h4>
                <div class="tags-list">
                    <?php 
                    $tags = explode(',', $post['tags']);
                    foreach ($tags as $tag): 
                        $tag = trim($tag);
                    ?>
                    <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Related Articles -->
    <?php if (!empty($related_posts)): ?>
    <section class="related-articles">
        <div class="container">
            <h2>Related Articles</h2>
            <div class="related-grid">
                <?php foreach ($related_posts as $related): ?>
                <article class="related-article">
                    <div class="related-image">
                        <img src="<?php echo $related['featured_image'] ?: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop'; ?>" alt="<?php echo htmlspecialchars($related['title']); ?>">
                        <div class="related-category"><?php echo htmlspecialchars($related['category']); ?></div>
                    </div>
                    <div class="related-content">
                        <div class="related-meta">
                            <span><?php echo date('M j, Y', strtotime($related['published_at'])); ?></span>
                        </div>
                        <h3><a href="<?php echo siteUrl('news/' . $related['slug']); ?>"><?php echo htmlspecialchars($related['title']); ?></a></h3>
                        <p><?php echo htmlspecialchars(substr($related['excerpt'], 0, 120)) . '...'; ?></p>
                        <a href="<?php echo siteUrl('news/' . $related['slug']); ?>" class="read-more">Read More →</a>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Back to News -->
    <section class="back-to-news">
        <div class="container">
            <a href="<?php echo siteUrl('news'); ?>" class="back-link">← Back to All News</a>
        </div>
    </section>

    <?php loadTemplate('footer'); ?>
</body>
</html>
