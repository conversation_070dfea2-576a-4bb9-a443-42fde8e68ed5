<?php
/**
 * Projects Page - Portfolio Showcase
 * Displays our architectural and design projects
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

$pageTitle = 'Our Projects - Architectural Excellence Showcase';
$pageDescription = 'Explore our portfolio of completed projects including commercial buildings, residential developments, and innovative architectural designs.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Projects Page Specific CSS -->
    <style>
        /* ===== PROJECTS PAGE STYLES ===== */
        .projects-hero {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            padding: 8rem 0 6rem;
            position: relative;
            overflow: hidden;
        }
        
        .projects-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(230, 126, 34, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(230, 126, 34, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .projects-hero-content {
            position: relative;
            z-index: 1;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .projects-hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            color: var(--accent-color);
        }
        
        .projects-hero p {
            font-size: 1.3rem;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }
        
        /* Filter Tabs */
        .project-filters {
            padding: 4rem 0 2rem;
            background: #f8f9fa;
        }
        
        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .filter-tab {
            padding: 1rem 2rem;
            background: white;
            border: 2px solid #e9ecef;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-weight: 600;
        }
        
        .filter-tab:hover,
        .filter-tab.active {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
            text-decoration: none;
        }
        
        /* Projects Grid */
        .projects-grid {
            padding: 4rem 0 8rem;
            background: #f8f9fa;
        }
        
        .projects-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 3rem;
        }
        
        .project-card {
            background: white;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
        }
        
        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }
        
        .project-image {
            height: 300px;
            overflow: hidden;
            position: relative;
        }
        
        .project-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .project-card:hover .project-image img {
            transform: scale(1.1);
        }
        
        .project-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(230, 126, 34, 0.9), rgba(0, 0, 0, 0.7));
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .project-card:hover .project-overlay {
            opacity: 1;
        }
        
        .project-view-btn {
            background: white;
            color: var(--accent-color);
            padding: 1rem 2rem;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .project-view-btn:hover {
            transform: scale(1.1);
            color: var(--accent-color);
            text-decoration: none;
        }
        
        .project-info {
            padding: 2.5rem;
        }
        
        .project-category {
            color: var(--accent-color);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 1rem;
        }
        
        .project-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .project-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .project-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .project-year {
            color: #999;
            font-size: 0.9rem;
        }
        
        .project-location {
            color: #666;
            font-size: 0.9rem;
        }
        
        /* Featured Project Section */
        .featured-project {
            padding: 8rem 0;
            background: white;
        }
        
        .featured-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6rem;
            align-items: center;
        }
        
        .featured-image {
            position: relative;
        }
        
        .featured-image img {
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .featured-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .featured-info h2 {
            color: var(--accent-color);
            font-size: 2.8rem;
            margin-bottom: 1rem;
        }
        
        .featured-category {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 2rem;
            font-weight: 300;
        }
        
        .featured-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .featured-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: #f8f9fa;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--accent-color);
            display: block;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .featured-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }
            
            .projects-hero h1 {
                font-size: 2.5rem;
            }
            
            .featured-info h2 {
                font-size: 2.2rem;
            }
            
            .projects-container {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .projects-hero {
                padding: 6rem 0 4rem;
            }
            
            .project-filters,
            .projects-grid,
            .featured-project {
                padding: 4rem 0;
            }
            
            .filter-tabs {
                flex-direction: column;
                align-items: center;
            }
            
            .featured-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Projects Hero Section -->
    <section class="projects-hero">
        <div class="container">
            <div class="projects-hero-content">
                <h1>Our Projects</h1>
                <p>Discover our portfolio of architectural excellence. From innovative commercial buildings to stunning residential developments, each project represents our commitment to design excellence, structural integrity, and client satisfaction.</p>
            </div>
        </div>
    </section>

    <!-- Featured Project -->
    <section class="featured-project">
        <div class="container">
            <div class="featured-content">
                <div class="featured-image">
                    <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop" alt="Metropolitan Office Complex">
                </div>
                <div class="featured-info">
                    <h2>Metropolitan Office Complex</h2>
                    <div class="featured-category">Commercial Architecture</div>
                    <div class="featured-description">
                        <p>Our flagship commercial project featuring state-of-the-art design, sustainable technologies, and modern workspace solutions. This 40-story complex sets new standards for urban commercial architecture.</p>
                        
                        <p>The building incorporates advanced energy-efficient systems, intelligent lighting, and flexible workspace configurations to meet the evolving needs of modern businesses.</p>
                    </div>
                    
                    <div class="featured-stats">
                        <div class="stat-item">
                            <span class="stat-number">40</span>
                            <span class="stat-label">Floors</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">2.1M</span>
                            <span class="stat-label">Sq Ft</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">5000</span>
                            <span class="stat-label">Workers</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">LEED</span>
                            <span class="stat-label">Platinum</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Filters -->
    <section class="project-filters">
        <div class="container">
            <div class="filter-tabs">
                <a href="#" class="filter-tab active" data-filter="all">All Projects</a>
                <a href="#" class="filter-tab" data-filter="commercial">Commercial</a>
                <a href="#" class="filter-tab" data-filter="residential">Residential</a>
                <a href="#" class="filter-tab" data-filter="institutional">Institutional</a>
                <a href="#" class="filter-tab" data-filter="renovation">Renovation</a>
            </div>
        </div>
    </section>

    <!-- Projects Grid -->
    <section class="projects-grid">
        <div class="container">
            <div class="projects-container">
                <!-- Project 1 -->
                <div class="project-card" data-category="commercial">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=600&h=400&fit=crop" alt="Tech Innovation Hub">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/tech-innovation-hub'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Commercial</div>
                        <h3 class="project-title">Tech Innovation Hub</h3>
                        <p class="project-description">Modern tech campus featuring collaborative workspaces, research labs, and sustainable design elements. A 15-story complex designed for innovation and growth.</p>
                        <div class="project-meta">
                            <span class="project-year">2023</span>
                            <span class="project-location">San Francisco, CA</span>
                        </div>
                    </div>
                </div>

                <!-- Project 2 -->
                <div class="project-card" data-category="residential">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=600&h=400&fit=crop" alt="Luxury Residential Complex">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/luxury-residential-complex'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Residential</div>
                        <h3 class="project-title">Luxury Residential Complex</h3>
                        <p class="project-description">Exclusive residential development featuring 120 luxury units with panoramic city views, premium amenities, and sophisticated architectural design.</p>
                        <div class="project-meta">
                            <span class="project-year">2023</span>
                            <span class="project-location">Miami, FL</span>
                        </div>
                    </div>
                </div>

                <!-- Project 3 -->
                <div class="project-card" data-category="institutional">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1541339907198-e08756dedf3f?w=600&h=400&fit=crop" alt="University Research Center">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/university-research-center'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Institutional</div>
                        <h3 class="project-title">University Research Center</h3>
                        <p class="project-description">State-of-the-art research facility featuring advanced laboratories, lecture halls, and collaborative spaces designed to foster academic excellence.</p>
                        <div class="project-meta">
                            <span class="project-year">2022</span>
                            <span class="project-location">Boston, MA</span>
                        </div>
                    </div>
                </div>

                <!-- Project 4 -->
                <div class="project-card" data-category="commercial">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=600&h=400&fit=crop" alt="Retail Shopping Center">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/retail-shopping-center'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Commercial</div>
                        <h3 class="project-title">Retail Shopping Center</h3>
                        <p class="project-description">Contemporary shopping destination combining retail spaces, dining areas, and entertainment venues in an architecturally stunning complex.</p>
                        <div class="project-meta">
                            <span class="project-year">2022</span>
                            <span class="project-location">Los Angeles, CA</span>
                        </div>
                    </div>
                </div>

                <!-- Project 5 -->
                <div class="project-card" data-category="residential">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=600&h=400&fit=crop" alt="Modern Family Homes">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/modern-family-homes'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Residential</div>
                        <h3 class="project-title">Modern Family Homes</h3>
                        <p class="project-description">Sustainable family housing development featuring energy-efficient designs, smart home technology, and community-focused amenities.</p>
                        <div class="project-meta">
                            <span class="project-year">2022</span>
                            <span class="project-location">Seattle, WA</span>
                        </div>
                    </div>
                </div>

                <!-- Project 6 -->
                <div class="project-card" data-category="renovation">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=600&h=400&fit=crop" alt="Historic Building Renovation">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/historic-building-renovation'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Renovation</div>
                        <h3 class="project-title">Historic Building Renovation</h3>
                        <p class="project-description">Careful restoration of a 1920s landmark building, preserving historical character while incorporating modern functionality and safety features.</p>
                        <div class="project-meta">
                            <span class="project-year">2021</span>
                            <span class="project-location">Chicago, IL</span>
                        </div>
                    </div>
                </div>

                <!-- Project 7 -->
                <div class="project-card" data-category="institutional">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1576013551627-0cc20b96c2a7?w=600&h=400&fit=crop" alt="Medical Center Complex">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/medical-center-complex'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Institutional</div>
                        <h3 class="project-title">Medical Center Complex</h3>
                        <p class="project-description">Comprehensive healthcare facility designed with patient comfort and medical efficiency in mind, featuring advanced medical technology integration.</p>
                        <div class="project-meta">
                            <span class="project-year">2021</span>
                            <span class="project-location">Houston, TX</span>
                        </div>
                    </div>
                </div>

                <!-- Project 8 -->
                <div class="project-card" data-category="commercial">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=600&h=400&fit=crop" alt="Corporate Headquarters">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/corporate-headquarters'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Commercial</div>
                        <h3 class="project-title">Corporate Headquarters</h3>
                        <p class="project-description">Iconic corporate campus featuring innovative workspace design, executive facilities, and sustainable building practices for a Fortune 500 company.</p>
                        <div class="project-meta">
                            <span class="project-year">2021</span>
                            <span class="project-location">Austin, TX</span>
                        </div>
                    </div>
                </div>

                <!-- Project 9 -->
                <div class="project-card" data-category="residential">
                    <div class="project-image">
                        <img src="https://images.unsplash.com/photo-1605276374104-dee2a0ed3cd6?w=600&h=400&fit=crop" alt="Waterfront Condominiums">
                        <div class="project-overlay">
                            <a href="<?php echo siteUrl('projects/waterfront-condominiums'); ?>" class="project-view-btn">View Project</a>
                        </div>
                    </div>
                    <div class="project-info">
                        <div class="project-category">Residential</div>
                        <h3 class="project-title">Waterfront Condominiums</h3>
                        <p class="project-description">Luxurious waterfront living featuring floor-to-ceiling windows, private balconies, and premium finishes with stunning harbor views.</p>
                        <div class="project-meta">
                            <span class="project-year">2020</span>
                            <span class="project-location">San Diego, CA</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
    
    <!-- Project Filter JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            const projectCards = document.querySelectorAll('.project-card');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all tabs
                    filterTabs.forEach(t => t.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    this.classList.add('active');
                    
                    const filterValue = this.getAttribute('data-filter');
                    
                    // Filter projects
                    projectCards.forEach(card => {
                        if (filterValue === 'all' || card.getAttribute('data-category') === filterValue) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>
