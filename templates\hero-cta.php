<?php
/**
 * Reusable Hero CTA Template
 * Can be used across different pages with custom content
 */

if (!defined('MONOLITH_ACCESS')) {
    die('Direct access not permitted');
}

// Get hero settings from admin or use defaults
$hero_caption = isset($hero_data['caption']) ? $hero_data['caption'] : getThemeOption('hero_cta_caption', "Let's Connect");
$hero_title = isset($hero_data['title']) ? $hero_data['title'] : getThemeOption('hero_cta_title', 'Start Your Project Today');
$hero_description = isset($hero_data['description']) ? $hero_data['description'] : getThemeOption('hero_cta_description', "Our strategic and technical advisory service isn't based on a hunch. It's backed by years of experience, extensive technical knowledge and data-driven insights. With our trusted advice,");
$hero_button_text = isset($hero_data['button_text']) ? $hero_data['button_text'] : getThemeOption('hero_cta_button_text', 'GET IN TOUCH');
$hero_button_link = isset($hero_data['button_link']) ? $hero_data['button_link'] : getThemeOption('hero_cta_button_link', 'contact');
$hero_background = isset($hero_data['background']) ? $hero_data['background'] : getThemeOption('hero_cta_background', '');

// If custom background is provided, use it; otherwise use admin setting
if (isset($hero_data['background']) && !empty($hero_data['background'])) {
    $background_style = "background-image: url('" . $hero_data['background'] . "');";
} elseif (!empty($hero_background)) {
    $background_style = "background-image: url('" . siteUrl($hero_background) . "');";
} else {
    $background_style = '';
}
?>

<!-- Reusable Hero CTA Section -->
<section class="cta-section" <?php if (!empty($background_style)): ?>style="<?php echo $background_style; ?>"<?php endif; ?>>
    <div class="container">
        <div class="cta-wrap">
            <div class="cta-outer">
                <div class="cta-title">
                    <div class="caption"><?php echo htmlspecialchars($hero_caption); ?></div>
                    <h2 class="text-white"><?php echo htmlspecialchars($hero_title); ?></h2>
                </div>
                <p class="cta-info"><?php echo htmlspecialchars($hero_description); ?></p>
                <div class="cta-button">
                    <a href="<?php echo siteUrl($hero_button_link); ?>" class="primary-button primary-button-dark">
                        <div><?php echo htmlspecialchars($hero_button_text); ?></div>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
