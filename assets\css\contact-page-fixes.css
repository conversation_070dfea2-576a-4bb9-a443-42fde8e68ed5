/* ===== CONTACT PAGE SPECIFIC FIXES ===== */

/* Prevent horizontal overflow on contact page */
body.contact-page {
    overflow-x: hidden !important;
    max-width: 100% !important;
}

html {
    overflow-x: hidden !important;
    max-width: 100% !important;
}

/* Remove gap between contact form and map */
.contact-section + .map-section {
    padding: 0;
    margin-top: -20px;
}

.contact-section + .map-section .map-wrapper h2 {
    padding: 40px 0 20px 0;
}

/* Ensure map container has no gaps */
.map-section {
    margin-bottom: 0;
}

.map-container-full-width {
    margin-top: 0;
}

/* Remove spacing from service-hero-footer section (Ready to Get Started) */
.service-hero-footer {
    padding: 3rem 0 1rem 0 !important; /* Reduce from 6rem to 3rem top, 1rem bottom */
    margin: 0 !important;
}

/* Reduce spacing between CTA section and footer */
.service-hero-footer + .footer-component {
    margin-top: -20px;
}

/* ===== FIX MAP OVERFLOW ISSUE ===== */
/* Fix the full-width map container to prevent horizontal overflow */
.map-container-full-width {
    width: 100% !important; /* Use 100% instead of 100vw */
    position: static !important; /* Remove position relative */
    left: auto !important;
    right: auto !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
}

.map-container-full-width iframe {
    width: 100% !important;
    height: 450px !important;
    max-width: 100% !important;
    border: none !important;
    display: block !important;
    box-sizing: border-box !important;
}

/* Ensure map section doesn't cause overflow */
.map-section {
    overflow: hidden !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}
