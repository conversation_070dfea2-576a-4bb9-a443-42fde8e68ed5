<?php
/**
 * Contact Page - Root Level
 * This file serves the contact page content at the clean URL /contact
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Handle contact form submission
$form_message = '';
$form_status = '';
if ($_POST && isset($_POST['submit_contact'])) {
    $result = handleContactForm($_POST);
    $form_message = $result['message'];
    $form_status = $result['success'] ? 'success' : 'error';
}

// Get services for dropdown
$services = getServices();

// Page metadata
$page_title = 'Contact Us - ' . SITE_NAME;
$page_description = 'Get in touch with Monolith Design Co. for your next engineering or architectural project. Contact us today for a consultation.';
$page_keywords = 'contact, get in touch, consultation, engineering services, architectural services, project inquiry';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo themeUrl('images/favicon.ico'); ?>">
    
    <!-- Google Fonts - Arkify uses Public Sans and Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo themeUrl('css/arkify-style.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/work-section-clean.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/responsive.css'); ?>">

    <!-- Custom accent color -->
    <style>
        :root {
            --accent-color: <?php echo getThemeOption('accent_color', '#2D5A27'); ?>;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Page Hero Section -->
    <?php
    $hero_title = getThemeOption('contact_hero_title', 'Get In Touch');
    $hero_description = getThemeOption('contact_hero_description', 'Ready to start your project? We\'re here to help bring your vision to life with our expertise in engineering and architectural design.');
    $hero_background = getThemeOption('contact_hero_background', themeUrl('images/hero-bg-1.jpg'));
    $hero_bg_style = '';
    if (!empty($hero_background)) {
        $hero_bg_style = 'background-image: url(' . $hero_background . ');';
    }
    ?>
    <section class="page-hero" style="<?php echo $hero_bg_style; ?>">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title"><?php echo htmlspecialchars($hero_title); ?></h1>
                <p class="hero-description"><?php echo htmlspecialchars($hero_description); ?></p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <div class="contact-wrapper">
                <!-- Contact Information -->
                <div class="contact-info">
                    <div class="contact-info-content">
                        <h2><?php echo getThemeOption('contact_info_title', 'Contact Information'); ?></h2>
                        <p><?php echo getThemeOption('contact_info_description', 'Get in touch with us today to discuss your project requirements.'); ?></p>
                        
                        <div class="contact-details">
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                        <circle cx="12" cy="10" r="3"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <h4>Address</h4>
                                    <p><?php echo nl2br(htmlspecialchars(getThemeOption('address', '123 Design Street\nArchitecture City, AC 12345'))); ?></p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <h4>Phone</h4>
                                    <p><?php echo htmlspecialchars(getThemeOption('phone_number', '+****************')); ?></p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                        <polyline points="22,6 12,13 2,6"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <h4>Email</h4>
                                    <p><?php echo htmlspecialchars(getThemeOption('email', '<EMAIL>')); ?></p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <polyline points="12,6 12,12 16,14"/>
                                    </svg>
                                </div>
                                <div class="contact-text">
                                    <h4>Business Hours</h4>
                                    <p><?php echo nl2br(htmlspecialchars(getThemeOption('business_hours', 'Monday - Friday: 8:00 AM - 6:00 PM\nSaturday: 9:00 AM - 4:00 PM\nSunday: Closed'))); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="contact-form">
                    <div class="contact-form-content">
                        <h2><?php echo getThemeOption('contact_form_title', 'Send us a Message'); ?></h2>
                        <p><?php echo getThemeOption('contact_form_description', 'Fill out the form below and we\'ll get back to you as soon as possible.'); ?></p>

                        <?php if ($form_message): ?>
                            <div class="form-message <?php echo $form_status; ?>">
                                <?php echo htmlspecialchars($form_message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="" class="contact-form-inner">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name">Full Name *</label>
                                    <input type="text" id="name" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label for="email">Email Address *</label>
                                    <input type="email" id="email" name="email" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone">
                                </div>
                                <div class="form-group">
                                    <label for="service">Service Interested In</label>
                                    <select id="service" name="service">
                                        <option value="">Select a service...</option>
                                        <?php if (!empty($services)): ?>
                                            <?php foreach ($services as $service): ?>
                                                <option value="<?php echo htmlspecialchars($service['title']); ?>">
                                                    <?php echo htmlspecialchars($service['title']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="message">Message *</label>
                                <textarea id="message" name="message" rows="5" required placeholder="Tell us about your project..."></textarea>
                            </div>

                            <button type="submit" name="submit_contact" class="submit-btn">
                                Send Message
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section (Optional) -->
    <?php if (getThemeOption('contact_show_map', '1') === '1'): ?>
    <section class="map-section">
        <div class="container">
            <div class="map-wrapper">
                <h2><?php echo getThemeOption('contact_map_title', 'Find Us'); ?></h2>
                <div class="map-container">
                    <?php 
                    $map_embed = getThemeOption('contact_map_embed', '');
                    if (!empty($map_embed)): 
                        echo $map_embed;
                    else: 
                    ?>
                        <div class="map-placeholder">
                            <p>Map integration available in admin panel</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Hero CTA Section -->
    <?php loadTemplate('hero-cta'); ?>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
    <script>
        // Form validation and enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.contact-form-inner');
            const submitBtn = form.querySelector('.submit-btn');
            
            form.addEventListener('submit', function(e) {
                // Show loading state
                submitBtn.innerHTML = `
                    <span>Sending...</span>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="animate-spin">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                    </svg>
                `;
                submitBtn.disabled = true;
            });

            // Auto-hide success/error messages after 5 seconds
            const formMessage = document.querySelector('.form-message');
            if (formMessage) {
                setTimeout(() => {
                    formMessage.style.opacity = '0';
                    setTimeout(() => {
                        formMessage.style.display = 'none';
                    }, 300);
                }, 5000);
            }
        });
    </script>
</body>
</html>
