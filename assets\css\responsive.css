/**
 * Monolith Design Co. - Responsive Styles
 * Mobile and tablet specific styling
 */

/* Tablet Styles (768px - 1199px) */
@media (max-width: 1199px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }
    
    .projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .footer-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

/* Mobile Landscape / Small Tablet (768px - 991px) */
@media (max-width: 991px) {
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .slide-title {
        font-size: 2.5rem;
    }
    
    .philosophy-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .nav-menu {
        gap: var(--spacing-md);
    }
    
    .nav-link {
        font-size: 0.8rem;
    }
}

/* Mobile Portrait (768px and below) */
@media (max-width: 768px) {
    /* Hide desktop navigation */
    .main-navigation {
        display: none;
    }
    
    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: flex;
    }
    
    /* Mobile navigation styles */
    .mobile-navigation {
        position: fixed;
        top: 72px;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        z-index: 999;
        overflow-y: auto;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        backdrop-filter: blur(10px);
    }

    .mobile-navigation.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .mobile-nav-content {
        padding: var(--spacing-xl) var(--spacing-lg);
        animation: slideInUp 0.6s ease-out 0.2s both;
    }

    .mobile-nav-menu {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .mobile-nav-link {
        display: block;
        padding: var(--spacing-md) var(--spacing-lg);
        font-family: var(--font-heading);
        font-weight: 600;
        color: var(--text-color);
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 1.1rem;
        text-decoration: none;
        transition: all 0.3s ease;
        border-radius: 12px;
        position: relative;
        overflow: hidden;
        background: transparent;
        border: 2px solid transparent;
    }

    .mobile-nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(230, 126, 34, 0.1), transparent);
        transition: left 0.6s ease;
    }

    .mobile-nav-link:hover::before {
        left: 100%;
    }

    .mobile-nav-link:hover {
        color: var(--accent-color);
        background: linear-gradient(135deg, rgba(230, 126, 34, 0.05) 0%, rgba(255, 107, 53, 0.05) 100%);
        border-color: var(--accent-color);
        transform: translateX(8px);
        box-shadow: 0 4px 15px rgba(230, 126, 34, 0.2);
    }
    
    .mobile-submenu {
        display: none;
        padding-left: var(--spacing-md);
        background-color: var(--secondary-color);
        margin-top: var(--spacing-sm);
        border-radius: var(--radius-small);
    }
    
    .submenu-link {
        font-size: 0.875rem;
        text-transform: none;
        padding: var(--spacing-sm) 0;
    }
    
    .submenu-toggle {
        float: right;
        font-size: 1.2rem;
        color: var(--accent-color);
        cursor: pointer;
        user-select: none;
    }
    
    .mobile-cta {
        margin-top: var(--spacing-lg);
        text-align: center;
    }
    
    /* Header adjustments */
    .header-content {
        padding: var(--spacing-sm) 0;
    }
    
    .logo-img {
        height: 35px;
    }
    
    /* Typography adjustments */
    h1 {
        font-size: 2rem;
        line-height: 1.1;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    h3 {
        font-size: 1.5rem;
    }
    
    .slide-title {
        font-size: 2rem;
        margin-bottom: var(--spacing-sm);
    }
    
    .slide-subtitle {
        font-size: 1rem;
        margin-bottom: var(--spacing-lg);
    }
    
    /* Section adjustments */
    .section-padding {
        padding: var(--spacing-xxl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    /* Grid adjustments */
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .footer-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    /* Button adjustments */
    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: 0.9rem;
    }
    
    /* Slider adjustments */
    .slider-prev,
    .slider-next {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .slider-prev {
        left: var(--spacing-sm);
    }
    
    .slider-next {
        right: var(--spacing-sm);
    }
    
    .slider-dots {
        bottom: var(--spacing-md);
    }
    
    /* Footer adjustments */
    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .footer-legal {
        justify-content: center;
    }
    
    /* Social links */
    .social-links {
        gap: var(--spacing-sm);
    }
    
    .social-link {
        width: 40px;
        height: 40px;
    }
    
    /* Value items stack vertically */
    .core-values {
        gap: var(--spacing-md);
    }
    
    .value-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
    
    /* Testimonials */
    .testimonial-quote {
        font-size: 1.25rem;
        line-height: 1.5;
    }
}

/* Small Mobile (480px and below) */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    /* Further reduce typography */
    h1 {
        font-size: 1.75rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .slide-title {
        font-size: 1.75rem;
    }
    
    .slide-subtitle {
        font-size: 0.9rem;
    }
    
    /* Reduce section padding */
    .section-padding {
        padding: var(--spacing-xl) 0;
    }
    
    /* Service cards */
    .service-card {
        padding: var(--spacing-lg);
    }
    
    .service-icon img {
        width: 48px;
        height: 48px;
    }
    
    /* Project cards */
    .project-image {
        height: 200px;
    }
    
    /* Button adjustments */
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }
    
    /* Slider controls */
    .slider-prev,
    .slider-next {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    /* Footer logo */
    .footer-logo-img {
        height: 40px;
    }
    
    /* Testimonials */
    .testimonial-quote {
        font-size: 1.1rem;
    }
    
    /* Mobile menu */
    .mobile-nav-content {
        padding: var(--spacing-md);
    }
    
    .mobile-nav-link {
        font-size: 0.9rem;
        padding: var(--spacing-sm) 0;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Increase touch targets */
    .nav-link {
        padding: var(--spacing-sm) var(--spacing-xs);
    }
    
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .mobile-nav-link {
        min-height: 44px;
        padding: var(--spacing-md) 0;
    }
    
    /* Remove hover effects that don't work on touch */
    .service-card:hover {
        transform: none;
    }
    
    .project-card:hover {
        transform: none;
    }
    
    .btn:hover {
        transform: none;
    }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-slider {
        height: 70vh;
    }
    
    .slide-title {
        font-size: 1.5rem;
    }
    
    .slide-subtitle {
        font-size: 0.875rem;
        margin-bottom: var(--spacing-md);
    }
    
    .section-padding {
        padding: var(--spacing-lg) 0;
    }
}

/* Print styles */
@media print {
    .site-header,
    .mobile-menu-toggle,
    .mobile-navigation,
    .slider-nav,
    .slider-dots,
    .btn,
    .site-footer {
        display: none !important;
    }
    
    .hero-slider {
        height: auto;
        page-break-inside: avoid;
    }
    
    .slide {
        position: static;
        opacity: 1;
        background: none !important;
    }
    
    .slide-content {
        position: static;
        transform: none;
        color: var(--text-color) !important;
    }
    
    .section-padding {
        padding: var(--spacing-md) 0;
    }
    
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
}
