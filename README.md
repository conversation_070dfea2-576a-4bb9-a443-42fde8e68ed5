# 🏗️ Monolith Design Co. - Professional Engineering & Architecture Website

**Version:** 1.0.0 | **Release Date:** January 2025 | **License:** Envato Market License

A comprehensive, professional PHP website theme specifically designed for engineering and architectural firms. Features modern design, powerful admin dashboard, and complete content management system.

![Monolith Design Co. Preview](preview-image.jpg)

## ✨ What's Included

- **Complete Website Theme** - All pages and functionality ready to use
- **Admin Dashboard** - Comprehensive content management system
- **Documentation** - Detailed installation and usage guide
- **Demo Content** - Sample data to get you started quickly
- **Support Files** - Installation guide, changelog, license, credits

## 🎯 Perfect For

- **Engineering Firms** - Structural, civil, mechanical engineering companies
- **Architectural Studios** - Design firms and architectural consultancies  
- **Construction Companies** - General contractors and construction management
- **Infrastructure Developers** - Bridge, stadium, and large project developers
- **Consulting Firms** - Technical and project management consultants

## 🚀 Key Features

### 🎨 **Professional Design**
- Modern, clean aesthetic inspired by architectural precision
- 8-point grid system for perfect visual alignment
- Customizable color scheme (default: burnt orange accent)
- Mobile-first responsive design for all devices
- Professional typography (Montserrat + Lato)

### 📱 **Fully Responsive**
- **Desktop** (1200px+) - Full layout with sidebar navigation
- **Tablet** (768px-1199px) - Adapted layout for medium screens  
- **Mobile** (480px-767px) - Optimized mobile experience
- **Small Mobile** (<480px) - Compact layout for small screens

### 🛠️ **Complete Admin Dashboard**
- **Theme Options** - Colors, logos, contact information
- **Slider Management** - Homepage hero slider with unlimited slides
- **Services Management** - Add/edit service pages with icons
- **Projects Portfolio** - Upload and manage portfolio items
- **Team Management** - Staff profiles with social media links
- **Blog System** - News and articles with categories
- **Contact Forms** - Form submissions and inquiry management
- **Footer Customization** - Complete footer control

### 📄 **Pages Included**
- **Homepage** - Hero slider, services, projects, testimonials, CTA
- **About Us** - Company story, mission, values, team showcase
- **Services** - Service overview with individual service pages
- **Projects** - Portfolio gallery with filtering and individual project pages
- **Team** - Staff profiles with enhanced CEO details
- **News/Blog** - Articles and insights with individual post pages
- **Contact** - Contact form, map integration, business information

### 🔧 **Technical Features**
- **Clean URLs** - SEO-friendly URLs (no .php extensions)
- **Security** - SQL injection protection, XSS prevention, CSRF tokens
- **Performance** - Optimized CSS/JS, image compression, caching headers
- **SEO Ready** - Meta tags, structured data, Open Graph integration
- **File Uploads** - Secure image upload system with validation
- **Database Driven** - MySQL database for all content management

## 📋 Requirements

- **PHP:** 7.4 or higher (8.0+ recommended)
- **MySQL:** 5.7 or higher (8.0+ recommended)  
- **Web Server:** Apache with mod_rewrite enabled
- **Memory:** 128MB minimum (256MB recommended)
- **Disk Space:** 50MB minimum
- **PHP Extensions:** PDO, PDO_MySQL, GD, JSON, Session

## ⚡ Quick Installation

1. **Upload** theme files to your web server
2. **Create** MySQL database 
3. **Visit** `yoursite.com/install.php`
4. **Enter** database credentials and click install
5. **Delete** install.php file when prompted
6. **Access** admin panel at `yoursite.com/admin`

**Default Login:** admin / admin123 (⚠️ Change immediately!)

📖 **See INSTALLATION.txt for detailed setup instructions**

## 🎨 Customization

### **Easy Color Changes**
Update your brand colors through the admin panel or CSS variables:
```css
:root {
    --primary-color: #1A1A1A;    /* Dark elements */
    --secondary-color: #F5F5F5;  /* Light backgrounds */
    --accent-color: #E67E22;     /* Your brand color */
}
```

### **Logo & Branding**
- Upload your logo through admin panel (SVG recommended)
- Replace favicon with your brand icon
- Update company information and contact details

### **Content Management**
- **Services:** Add unlimited services with custom icons
- **Projects:** Upload portfolio with categories and filtering
- **Team:** Add staff members with social media integration  
- **Blog:** Publish articles and company news
- **Testimonials:** Showcase client feedback

## 🔗 Integrations

- **Google Maps** - Contact page location display
- **Google Fonts** - Typography (Montserrat, Lato)
- **Social Media** - Facebook, Twitter, LinkedIn, Instagram links
- **Newsletter** - Email subscription functionality
- **Contact Forms** - Email notifications for inquiries

## 🛡️ Security Features

- **SQL Injection Protection** - Prepared statements for all queries
- **XSS Prevention** - Input sanitization and validation
- **CSRF Protection** - Token-based form security
- **File Upload Security** - Type and size validation
- **Secure Sessions** - HTTP-only cookies and session management
- **Security Headers** - XSS protection, content type validation

## 📊 Performance Optimized

- **Minimal Dependencies** - Pure PHP, no heavy frameworks
- **Optimized Assets** - Compressed CSS and JavaScript
- **Browser Caching** - .htaccess rules for static assets
- **Image Optimization** - Lazy loading and compression
- **Database Efficiency** - Optimized queries and indexing

## 🌐 Browser Support

✅ **Fully Tested & Compatible:**
- Chrome 90+
- Firefox 88+  
- Safari 14+
- Edge 90+
- Opera 76+
- Mobile browsers (iOS Safari, Android Chrome)

## 📁 File Structure

```
monolith-design/
├── admin/                   # Admin dashboard
│   ├── index.php           # Theme options  
│   ├── services.php        # Service management
│   ├── projects.php        # Portfolio management
│   └── ...
├── assets/                 # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── images/            # Images and uploads
├── components/            # Reusable components
│   └── footer/           # Modular footer system
├── includes/             # Core functionality
│   └── functions.php     # Helper functions
├── pages/               # Individual pages
│   ├── about.php       # About page
│   ├── services.php    # Services overview
│   └── ...
├── templates/          # Template files
│   ├── header.php     # Site header
│   ├── footer.php     # Site footer  
│   └── ...
├── config.php         # Configuration
├── database.sql       # Database structure
├── .htaccess         # URL rewriting & security
└── documentation.html # Complete documentation
```

## 📞 Support & Documentation

- **📖 Complete Documentation** - See `documentation.html`
- **🚀 Installation Guide** - See `INSTALLATION.txt`  
- **📝 Changelog** - See `Changelog.txt`
- **⚖️ License** - See `License.txt`
- **🙏 Credits** - See `Credits.txt`

## 🔄 Updates & Versions

**v1.0.0** - January 2025
- ✨ Initial release with complete functionality
- 🎨 Professional design system
- 🛠️ Full admin dashboard
- 📱 Mobile responsive design
- 🔐 Security features
- 📊 Performance optimization

## ⭐ What Makes This Theme Special

### **Built for Professionals**
Unlike generic business themes, Monolith Design Co. is specifically crafted for engineering and architectural firms with industry-specific features and terminology.

### **Complete Solution**
Everything you need is included - no additional plugins or services required. Just install, customize, and launch.

### **Production Ready**
Thoroughly tested on multiple hosting environments with security best practices and performance optimization built-in.

### **Easy to Customize**
Comprehensive admin panel means you can customize everything without touching code. Yet the code is clean and well-documented for developers.

### **Future Proof**
Built with modern PHP standards and responsive design principles to ensure longevity and compatibility.

## 🎯 Demo & Preview

**Live Demo:** [View Demo Site](#) *(Replace with your demo URL)*
**Admin Demo:** [View Admin Panel](#) *(Replace with admin demo)*

## 🏆 Why Choose Monolith Design Co.?

✅ **Industry Specific** - Built specifically for engineering/architecture  
✅ **Complete Package** - Everything included, nothing extra needed
✅ **Easy Setup** - 5-minute installation with auto-installer
✅ **Professional Design** - Modern, clean, and impressive
✅ **Fully Responsive** - Perfect on all devices
✅ **SEO Optimized** - Built for search engine visibility  
✅ **Secure & Fast** - Production-ready performance
✅ **Great Support** - Comprehensive documentation and support

## 📝 License

This theme is licensed under the **Envato Market License**. See `License.txt` for complete terms.

- ✅ Use for one website (Regular License)
- ✅ Use for multiple client websites (Extended License available)
- ✅ Customize and modify as needed
- ❌ Redistribute or resell as template

---

**🏗️ Built with precision, designed for excellence.**

*Monolith Design Co. - Engineering the Future of Structures*

**Like this theme?** ⭐ Please consider leaving a positive review!

## 🚀 Installation

### Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite
- At least 50MB disk space

### Quick Setup
1. **Download/Extract** the files to your web server directory
2. **Configure Database** - Edit `config.php` with your database credentials
3. **Run Installer** - Visit `yoursite.com/install.php` in your browser
4. **Delete Installer** - Remove `install.php` after successful installation
5. **Access Website** - Visit your homepage

### Manual Database Setup
If the automatic installer doesn't work:
1. Create a MySQL database named `monolith_design`
2. Import the `database.sql` file
3. Update database credentials in `config.php`

## 📁 File Structure

```
monolith-design/
├── assets/
│   ├── css/
│   │   ├── style.css          # Main stylesheet
│   │   └── responsive.css     # Mobile responsive styles
│   ├── js/
│   │   └── main.js           # Interactive functionality
│   └── images/
│       └── uploads/          # User uploaded images
├── includes/
│   └── functions.php         # Core functions and database
├── templates/
│   ├── header.php           # Site header template
│   └── footer.php           # Site footer template
├── pages/                   # Individual page files
├── admin/                   # Admin dashboard (to be created)
├── config.php              # Site configuration
├── index.php               # Homepage
├── database.sql            # Database structure and data
├── install.php             # Database installer
├── .htaccess               # URL rewriting rules
└── README.md               # This file
```

## 🎨 Customization

### Theme Colors
The accent color can be changed in the admin panel or by updating:
- CSS variable `--accent-color` in `style.css`
- Database theme option `accent_color`

### Adding Content
1. **Sliders** - Add/edit through admin panel
2. **Services** - Create service pages with descriptions
3. **Projects** - Upload project images and details
4. **Team Members** - Add staff profiles
5. **Testimonials** - Client feedback management

### Images
Replace placeholder images in `/assets/images/` with:
- **Logo** - Company logo (SVG recommended)
- **Hero backgrounds** - High-quality architectural images
- **Service icons** - Custom SVG icons
- **Project photos** - Portfolio images
- **Team photos** - Professional headshots

## 🔧 Configuration

### Database Settings (`config.php`)
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'monolith_design');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### Site Settings
```php
define('SITE_NAME', 'Your Company Name');
define('SITE_TAGLINE', 'Your Tagline');
define('SITE_URL', 'https://yourwebsite.com');
```

### Upload Settings
```php
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
```

## 📱 Responsive Design

The website is fully responsive with breakpoints at:
- **Desktop**: 1200px+ (full layout)
- **Tablet**: 768px-1199px (adapted layout)
- **Mobile**: 480px-767px (mobile layout)
- **Small Mobile**: <480px (compact layout)

## 🔒 Security Features

- **SQL Injection Protection** - Prepared statements
- **XSS Prevention** - Input sanitization
- **CSRF Protection** - Token-based forms
- **File Upload Validation** - Type and size restrictions
- **Secure Sessions** - HTTP-only cookies
- **Directory Protection** - .htaccess security headers

## 🌐 SEO Optimization

- **Clean URLs** - `/services/architectural-design/`
- **Meta Tags** - Title, description, keywords
- **Open Graph** - Social media sharing
- **Structured Data** - Schema.org markup
- **Image Optimization** - Alt tags and lazy loading
- **Page Speed** - Compressed assets and caching

## 📊 Performance

- **Optimized CSS** - Organized with CSS variables
- **Minimal JavaScript** - Vanilla JS, no jQuery
- **Image Lazy Loading** - Improves page speed
- **Caching Headers** - Browser caching enabled
- **Compressed Assets** - Gzip compression

## 🎯 Browser Support

- **Chrome** 60+
- **Firefox** 60+
- **Safari** 12+
- **Edge** 79+
- **Mobile browsers** (iOS Safari, Chrome Mobile)

## 🚨 Troubleshooting

### Common Issues

**Database Connection Error**
- Check credentials in `config.php`
- Verify MySQL server is running
- Ensure database exists

**Images Not Loading**
- Check file permissions on `/assets/images/uploads/`
- Verify image paths in database
- Ensure uploaded files are within size limits

**Clean URLs Not Working**
- Enable Apache mod_rewrite
- Check .htaccess file permissions
- Verify AllowOverride is enabled

**Mobile Menu Not Working**
- Check JavaScript console for errors
- Ensure main.js is loading properly
- Verify mobile breakpoint CSS

## 🔄 Updates

### Updating Content
1. Use the admin panel for most content updates
2. Images can be uploaded through the admin interface
3. Theme options can be changed without code modification

### Code Updates
1. Backup your database and files
2. Update core files (preserving config.php)
3. Run any database migrations if needed

## 📞 Support

For support with this theme:
1. Check this README file
2. Review the code comments
3. Test on a development environment first

## 📝 License

This theme is created for professional use. Customize and modify as needed for your projects.

---

**Monolith Design Co.** - Engineering the Future of Structures

Built with precision, designed for excellence. 🏗️
