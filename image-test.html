<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Test - Monolith Design</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .image-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: #f9f9f9;
        }
        
        .image-container {
            height: 200px;
            background: #f5f5f5;
            position: relative;
            overflow: hidden;
        }
        
        .test-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            display: block;
        }
        
        .image-info {
            padding: 10px;
            background: white;
        }
        
        .status {
            font-weight: bold;
        }
        
        .status.loading { color: orange; }
        .status.loaded { color: #E67E22; }
        .status.error { color: red; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Image Loading Test - Featured Work Images</h1>
        <p>Testing the project images used in the Featured Work section:</p>
        
        <div class="image-grid">
            <div class="image-item">
                <div class="image-container">
                    <img src="assets/images/demo-image/demo-images/imgi_61_678b4fa8626623b854fc160e_project-main01-p-1600.jpg" 
                         alt="Modern Office Complex" class="test-image" data-name="Main Project Image">
                </div>
                <div class="image-info">
                    <h3>Modern Office Complex</h3>
                    <p class="status loading">Loading...</p>
                    <small>File: imgi_61_678b4fa8626623b854fc160e_project-main01-p-1600.jpg</small>
                </div>
            </div>
            
            <div class="image-item">
                <div class="image-container">
                    <img src="assets/images/demo-image/demo-images/imgi_62_678b510a26ec52b2b74c5dc8_project-thumb02-p-1080.jpg" 
                         alt="Luxury Residential Tower" class="test-image" data-name="Residential Tower">
                </div>
                <div class="image-info">
                    <h3>Luxury Residential Tower</h3>
                    <p class="status loading">Loading...</p>
                    <small>File: imgi_62_678b510a26ec52b2b74c5dc8_project-thumb02-p-1080.jpg</small>
                </div>
            </div>
            
            <div class="image-item">
                <div class="image-container">
                    <img src="assets/images/demo-image/demo-images/imgi_64_678b51888f09cbd817b6271f_project-thumb06-1-p-1080.jpg" 
                         alt="Innovation Center" class="test-image" data-name="Innovation Center">
                </div>
                <div class="image-info">
                    <h3>Innovation Center</h3>
                    <p class="status loading">Loading...</p>
                    <small>File: imgi_64_678b51888f09cbd817b6271f_project-thumb06-1-p-1080.jpg</small>
                </div>
            </div>
            
            <div class="image-item">
                <div class="image-container">
                    <img src="assets/images/demo-image/demo-images/imgi_66_678b5173e98ce8355a1e0afb_project-thumb05-1-p-1080.jpg"
                         alt="Sustainable Housing Development" class="test-image" data-name="Sustainable Housing">
                </div>
                <div class="image-info">
                    <h3>Sustainable Housing Development</h3>
                    <p class="status loading">Loading...</p>
                    <small>File: imgi_66_678b5173e98ce8355a1e0afb_project-thumb05-1-p-1080.jpg</small>
                </div>
            </div>

            <div class="image-item">
                <div class="image-container">
                    <img src="assets/images/demo-image/demo-images/imgi_42_678b4fa8626623b854fc160e_project-main01-p-800.jpg"
                         alt="Stadium Project" class="test-image" data-name="Stadium Project">
                </div>
                <div class="image-info">
                    <h3>Stadium Project</h3>
                    <p class="status loading">Loading...</p>
                    <small>File: imgi_42_678b4fa8626623b854fc160e_project-main01-p-800.jpg</small>
                </div>
            </div>

            <div class="image-item">
                <div class="image-container">
                    <img src="assets/images/demo-image/demo-images/imgi_9_678b510a26ec52b2b74c5dc8_project-thumb02.jpg"
                         alt="Bridge Engineering" class="test-image" data-name="Bridge Engineering">
                </div>
                <div class="image-info">
                    <h3>Bridge Engineering</h3>
                    <p class="status loading">Loading...</p>
                    <small>File: imgi_9_678b510a26ec52b2b74c5dc8_project-thumb02.jpg</small>
                </div>
            </div>
        </div>
        
        <h2>Test Results:</h2>
        <div id="results"></div>
        
        <p><a href="index.php">← Back to Main Site</a></p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('.test-image');
            const results = document.getElementById('results');
            let loadedCount = 0;
            let errorCount = 0;
            
            images.forEach(img => {
                const statusEl = img.closest('.image-item').querySelector('.status');
                const name = img.getAttribute('data-name');
                
                img.addEventListener('load', function() {
                    statusEl.textContent = 'Loaded Successfully';
                    statusEl.className = 'status loaded';
                    loadedCount++;
                    updateResults();
                });
                
                img.addEventListener('error', function() {
                    statusEl.textContent = 'Failed to Load';
                    statusEl.className = 'status error';
                    errorCount++;
                    updateResults();
                    console.error('Failed to load:', img.src);
                });
                
                // Handle already loaded images
                if (img.complete && img.naturalHeight !== 0) {
                    img.dispatchEvent(new Event('load'));
                }
            });
            
            function updateResults() {
                const total = images.length;
                const pending = total - loadedCount - errorCount;
                
                results.innerHTML = `
                    <p><strong>Total Images:</strong> ${total}</p>
                    <p><strong>Loaded:</strong> ${loadedCount}</p>
                    <p><strong>Failed:</strong> ${errorCount}</p>
                    <p><strong>Pending:</strong> ${pending}</p>
                `;
                
                if (pending === 0) {
                    results.innerHTML += `<p><strong>All images processed!</strong></p>`;
                }
            }
            
            updateResults();
        });
    </script>
</body>
</html>
