<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34495e;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#heroGradient1)"/>
  
  <!-- Abstract building shapes -->
  <rect x="100" y="400" width="200" height="680" fill="rgba(255,255,255,0.1)" />
  <rect x="350" y="300" width="150" height="780" fill="rgba(255,255,255,0.08)" />
  <rect x="550" y="450" width="180" height="630" fill="rgba(255,255,255,0.12)" />
  <rect x="780" y="200" width="220" height="880" fill="rgba(255,255,255,0.09)" />
  <rect x="1050" y="350" width="160" height="730" fill="rgba(255,255,255,0.11)" />
  <rect x="1260" y="250" width="190" height="830" fill="rgba(255,255,255,0.07)" />
  <rect x="1500" y="400" width="150" height="680" fill="rgba(255,255,255,0.1)" />
  
  <!-- Grid overlay -->
  <defs>
    <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)" />
  
  <text x="960" y="540" font-family="Montserrat, Arial, sans-serif" font-size="48" fill="white" text-anchor="middle" opacity="0.3">Modern Architecture</text>
</svg>
