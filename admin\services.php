<?php
/**
 * Services Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Simple authentication - session already started in functions.php
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// Handle file upload function
function handleIconUpload($file, $service_slug) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    $allowed_types = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
    $allowed_extensions = ['svg', 'png', 'jpg', 'jpeg'];

    $file_info = pathinfo($file['name']);
    $extension = strtolower($file_info['extension']);

    if (!in_array($file['type'], $allowed_types) || !in_array($extension, $allowed_extensions)) {
        return false;
    }

    // Create filename: service-slug.extension
    $filename = $service_slug . '.' . $extension;
    $upload_path = '../assets/images/uploads/services/' . $filename;

    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return 'assets/images/uploads/services/' . $filename;
    }

    return false;
}

// Handle form submissions
$message = '';
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_service':
                $title = sanitizeInput($_POST['title']);
                $slug = sanitizeInput($_POST['slug']);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content']; // Allow HTML
                $icon = sanitizeInput($_POST['icon']);
                $sort_order = intval($_POST['sort_order']);

                // Handle icon upload
                if (isset($_FILES['icon_upload']) && $_FILES['icon_upload']['error'] === UPLOAD_ERR_OK) {
                    $uploaded_icon = handleIconUpload($_FILES['icon_upload'], $slug);
                    if ($uploaded_icon) {
                        $icon = $uploaded_icon;
                    } else {
                        $message = 'Error uploading icon. Please check file format (SVG, PNG, JPG allowed).';
                        break;
                    }
                }

                $db = Database::getConnection();
                $stmt = $db->prepare("INSERT INTO services (title, slug, description, content, icon, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
                if ($stmt->execute([$title, $slug, $description, $content, $icon, $sort_order])) {
                    $message = 'Service added successfully!';
                } else {
                    $message = 'Error adding service.';
                }
                break;
                
            case 'update_service':
                $id = intval($_POST['id']);
                $title = sanitizeInput($_POST['title']);
                $slug = sanitizeInput($_POST['slug']);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content']; // Allow HTML
                $icon = sanitizeInput($_POST['icon']);
                $sort_order = intval($_POST['sort_order']);
                $active = isset($_POST['active']) ? 1 : 0;

                // Handle icon upload
                if (isset($_FILES['icon_upload']) && $_FILES['icon_upload']['error'] === UPLOAD_ERR_OK) {
                    $uploaded_icon = handleIconUpload($_FILES['icon_upload'], $slug);
                    if ($uploaded_icon) {
                        $icon = $uploaded_icon;

                        // Delete old uploaded icon if it exists and is in uploads folder
                        $db = Database::getConnection();
                        $stmt = $db->prepare("SELECT icon FROM services WHERE id = ?");
                        $stmt->execute([$id]);
                        $old_service = $stmt->fetch();
                        if ($old_service && strpos($old_service['icon'], 'uploads/services/') !== false) {
                            $old_icon_path = '../' . $old_service['icon'];
                            if (file_exists($old_icon_path)) {
                                unlink($old_icon_path);
                            }
                        }
                    } else {
                        $message = 'Error uploading icon. Please check file format (SVG, PNG, JPG allowed).';
                        break;
                    }
                }

                $db = Database::getConnection();
                $stmt = $db->prepare("UPDATE services SET title = ?, slug = ?, description = ?, content = ?, icon = ?, sort_order = ?, active = ? WHERE id = ?");
                if ($stmt->execute([$title, $slug, $description, $content, $icon, $sort_order, $active, $id])) {
                    $message = 'Service updated successfully!';
                } else {
                    $message = 'Error updating service.';
                }
                break;
                
            case 'delete_service':
                $id = intval($_POST['id']);
                $db = Database::getConnection();
                $stmt = $db->prepare("DELETE FROM services WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'Service deleted successfully!';
                } else {
                    $message = 'Error deleting service.';
                }
                break;
        }
    }
}

// Get all services
$db = Database::getConnection();
$stmt = $db->query("SELECT * FROM services ORDER BY sort_order ASC");
$services = $stmt->fetchAll();

// Get service for editing
$edit_service = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $db->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_service = $stmt->fetch();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services Management - Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ecf0f1;
            color: #2c3e50;
        }
        
        .admin-header {
            background: #34495e;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav {
            background: #2c3e50;
            padding: 0;
            display: flex;
            overflow-x: auto;
        }
        
        .admin-nav a {
            color: #bdc3c7;
            text-decoration: none;
            padding: 1rem 1.5rem;
            white-space: nowrap;
            transition: background 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #34495e;
            color: white;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-card-header {
            background: #3498db;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
        }
        
        .admin-card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-success {
            background: #E67E22;
        }

        .btn-success:hover {
            background: #d35400;
        }
        
        .message {
            background: #d5edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
        }
        
        .services-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .services-table th,
        .services-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .services-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .services-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-active {
            color: #E67E22;
            font-weight: 600;
        }
        
        .status-inactive {
            color: #e74c3c;
            font-weight: 600;
        }
        
        .form-row {
            display: flex;
            gap: 1rem;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .icon-preview {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .icon-preview-image {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 8px;
            border: 2px solid #E67E22;
        }

        .icon-preview-image img {
            width: 32px;
            height: 32px;
            object-fit: contain;
        }

        .icon-upload-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .file-upload-area {
            position: relative;
        }

        .file-upload-area input[type="file"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px dashed #E67E22;
            border-radius: 8px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-area input[type="file"]:hover {
            border-color: #d35400;
            background: #fef9f5;
        }

        .upload-help {
            color: #666;
            font-size: 12px;
            margin-top: 0.25rem;
            display: block;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Services Management</h1>
        <div>
            <a href="<?php echo siteUrl(); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Website</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="services.php" class="active">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <!-- Add/Edit Service Form -->
        <div class="admin-card">
            <div class="admin-card-header">
                <?php echo $edit_service ? 'Edit Service' : 'Add New Service'; ?>
            </div>
            <div class="admin-card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="<?php echo $edit_service ? 'update_service' : 'add_service'; ?>">
                    <?php if ($edit_service): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_service['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title">Service Title</label>
                            <input type="text" id="title" name="title" value="<?php echo $edit_service ? htmlspecialchars($edit_service['title']) : ''; ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="slug">URL Slug</label>
                            <input type="text" id="slug" name="slug" value="<?php echo $edit_service ? htmlspecialchars($edit_service['slug']) : ''; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Short Description</label>
                        <textarea id="description" name="description" required><?php echo $edit_service ? htmlspecialchars($edit_service['description']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="content">Full Content (HTML allowed)</label>
                        <textarea id="content" name="content" style="height: 200px;"><?php echo $edit_service ? htmlspecialchars($edit_service['content']) : ''; ?></textarea>
                    </div>
                    
                    <!-- Icon Section -->
                    <div class="form-group">
                        <label>Service Icon</label>

                        <?php if ($edit_service && !empty($edit_service['icon'])): ?>
                            <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <div style="width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; background: white; border-radius: 8px; border: 2px solid #E67E22;">
                                        <?php
                                        $current_icon = $edit_service['icon'];
                                        if (strpos($current_icon, '.svg') !== false || strpos($current_icon, '.png') !== false || strpos($current_icon, '.jpg') !== false || strpos($current_icon, '.jpeg') !== false):
                                        ?>
                                            <img src="../<?php echo htmlspecialchars($current_icon); ?>" alt="Current Icon" style="width: 32px; height: 32px; object-fit: contain;">
                                        <?php else: ?>
                                            <span style="font-size: 12px; color: #666;">Icon</span>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <strong>Current Icon:</strong><br>
                                        <code style="background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-size: 12px;"><?php echo htmlspecialchars($current_icon); ?></code>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="icon-upload-grid">
                            <div class="file-upload-area">
                                <label for="icon_upload" style="font-weight: 500; margin-bottom: 0.5rem; display: block;">Upload New Icon</label>
                                <input type="file" id="icon_upload" name="icon_upload" accept=".svg,.png,.jpg,.jpeg" onchange="previewIcon(this)">
                                <small class="upload-help">Supported: SVG, PNG, JPG (recommended: SVG for best quality)</small>

                                <!-- Preview area for new upload -->
                                <div id="upload-preview" style="display: none; margin-top: 1rem; padding: 1rem; background: #e8f5e8; border-radius: 8px; border: 1px solid #c3e6cb;">
                                    <div style="display: flex; align-items: center; gap: 1rem;">
                                        <div style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; background: white; border-radius: 4px;">
                                            <img id="preview-image" src="" alt="Preview" style="width: 24px; height: 24px; object-fit: contain;">
                                        </div>
                                        <div>
                                            <strong style="color: #155724;">New Icon Preview</strong><br>
                                            <span id="preview-filename" style="font-size: 12px; color: #155724;"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label for="icon" style="font-weight: 500; margin-bottom: 0.5rem; display: block;">Or Enter Icon Path/Class</label>
                                <input type="text" id="icon" name="icon" value="<?php echo $edit_service ? htmlspecialchars($edit_service['icon']) : ''; ?>" placeholder="e.g., assets/images/icons/service.svg or fa-home" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px;">
                                <small class="upload-help">For existing icons or FontAwesome classes</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="sort_order">Sort Order</label>
                        <input type="number" id="sort_order" name="sort_order" value="<?php echo $edit_service ? $edit_service['sort_order'] : count($services) + 1; ?>" required>
                    </div>
                    
                    <?php if ($edit_service): ?>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="active" name="active" <?php echo $edit_service['active'] ? 'checked' : ''; ?>>
                                <label for="active">Active</label>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <button type="submit" class="btn btn-success">
                        <?php echo $edit_service ? 'Update Service' : 'Add Service'; ?>
                    </button>
                    
                    <?php if ($edit_service): ?>
                        <a href="services.php" class="btn">Cancel</a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <!-- Services List -->
        <div class="admin-card">
            <div class="admin-card-header">
                Existing Services (<?php echo count($services); ?>/9)
            </div>
            <div class="admin-card-body">
                <?php if (empty($services)): ?>
                    <p>No services found. Add your first service above.</p>
                <?php else: ?>
                    <table class="services-table">
                        <thead>
                            <tr>
                                <th>Icon</th>
                                <th>Order</th>
                                <th>Title</th>
                                <th>Slug</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($services as $service): ?>
                                <tr>
                                    <td>
                                        <div style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border-radius: 6px; border: 1px solid #E67E22;">
                                            <?php if (!empty($service['icon'])): ?>
                                                <?php if (strpos($service['icon'], '.svg') !== false || strpos($service['icon'], '.png') !== false || strpos($service['icon'], '.jpg') !== false || strpos($service['icon'], '.jpeg') !== false): ?>
                                                    <img src="../<?php echo htmlspecialchars($service['icon']); ?>" alt="<?php echo htmlspecialchars($service['title']); ?>" style="width: 20px; height: 20px; object-fit: contain;">
                                                <?php else: ?>
                                                    <span style="font-size: 10px; color: #666;">Icon</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span style="font-size: 10px; color: #999;">No Icon</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><?php echo $service['sort_order']; ?></td>
                                    <td><?php echo htmlspecialchars($service['title']); ?></td>
                                    <td><?php echo htmlspecialchars($service['slug']); ?></td>
                                    <td>
                                        <span class="<?php echo $service['active'] ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo $service['active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="services.php?edit=<?php echo $service['id']; ?>" class="btn" style="padding: 0.5rem 1rem; font-size: 0.9rem;">Edit</a>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this service?');">
                                            <input type="hidden" name="action" value="delete_service">
                                            <input type="hidden" name="id" value="<?php echo $service['id']; ?>">
                                            <button type="submit" class="btn btn-danger" style="padding: 0.5rem 1rem; font-size: 0.9rem;">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function previewIcon(input) {
            const previewDiv = document.getElementById('upload-preview');
            const previewImage = document.getElementById('preview-image');
            const previewFilename = document.getElementById('preview-filename');

            if (input.files && input.files[0]) {
                const file = input.files[0];
                const reader = new FileReader();

                // Check file type
                const allowedTypes = ['image/svg+xml', 'image/png', 'image/jpeg', 'image/jpg'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Please select a valid image file (SVG, PNG, JPG)');
                    input.value = '';
                    previewDiv.style.display = 'none';
                    return;
                }

                // Check file size (max 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert('File size must be less than 2MB');
                    input.value = '';
                    previewDiv.style.display = 'none';
                    return;
                }

                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    previewFilename.textContent = file.name;
                    previewDiv.style.display = 'block';
                };

                reader.readAsDataURL(file);
            } else {
                previewDiv.style.display = 'none';
            }
        }

        // Auto-generate slug from title
        document.getElementById('title').addEventListener('input', function() {
            const title = this.value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/-+/g, '-') // Replace multiple hyphens with single
                .trim('-'); // Remove leading/trailing hyphens

            document.getElementById('slug').value = slug;
        });
    </script>
</body>
</html>
